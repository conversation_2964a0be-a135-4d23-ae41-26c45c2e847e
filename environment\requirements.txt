# Common numerical computing libraries.
numpy>=1.25
pandas==2.1.4
pyarrow==15.0.2
scipy>=1.9.1
matplotlib
seaborn
mlflow==2.12.1 # OSS ML experiment tracking framework (similar to wandb).
psutil>=5.9.8 # Track system metrics
pynvml>=11.5 # Track GPU metrics with MLFlow
hydra_core >= 1.3.2 # Elegant application configuration from CLI + YAML files.
hydra_colorlog>=1.2.0 # Colorful log outputs.
hydra_useful_callbacks @ git+https://github.com/joncarter1/hydra_useful_callbacks.git
hydra_submitit_launcher >= 1.2.0 # Submit jobs to Slurm with Hydra.
beartype>=0.14 # Lightweight runtime type-checking.
tqdm>=4.64.1 # Make your for-loops show a smart progress meter.
ray[default]==2.9.3 # Framework for parallel computing in Python.
pyedflib # Read EDF files.
python-dotenv[cli]>=1.0.1 # Load secret environment variables from a '.env' file (kept out of version control).
