"""TimesNet数据加载器，用于AHI回归任务"""

import os
import sys
import pandas as pd
import numpy as np
import torch
from torch.utils.data import Dataset, DataLoader
from glob import glob
from typing import Dict, Tuple, List
import logging

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
sys.path.append(project_root)

from wav2sleep.settings import SAO2, ECG, ABD, THX, AIRFLOW

logger = logging.getLogger(__name__)


def normalize_ahi_piecewise(ahi: float) -> float:
    """分段归一化AHI值到[0,1]"""
    if ahi < 5:
        return ahi / 20.0          # [0,5] -> [0, 0.25]
    elif ahi < 15:
        return 0.25 + (ahi-5) / 40.0   # [5,15] -> [0.25, 0.5]
    elif ahi < 30:
        return 0.5 + (ahi-15) / 60.0   # [15,30] -> [0.5, 0.75]
    else:
        return 0.75 + min((ahi-30) / 120.0, 0.25)  # [30,∞] -> [0.75, 1.0]


def denormalize_ahi_piecewise(norm_ahi: float) -> float:
    """反归一化AHI值"""
    if norm_ahi <= 0.25:
        return norm_ahi * 20.0
    elif norm_ahi <= 0.5:
        return 5 + (norm_ahi - 0.25) * 40.0
    elif norm_ahi <= 0.75:
        return 15 + (norm_ahi - 0.5) * 60.0
    else:
        return 30 + (norm_ahi - 0.75) * 120.0


class TimesNetSleepDataset(Dataset):
    """TimesNet睡眠数据集，用于AHI回归"""
    
    def __init__(
        self, 
        data_folder: str,
        label_csv_path: str = './shhs1_label.csv',
        normalize_ahi: bool = True,
        split: str = 'train',
        train_ratio: float = 0.7,
        val_ratio: float = 0.15,
        random_seed: int = 42
    ):
        """
        初始化数据集
        
        Args:
            data_folder: 处理后数据的文件夹路径
            label_csv_path: AHI标签CSV文件路径
            normalize_ahi: 是否归一化AHI值
            split: 数据集分割 ('train', 'val', 'test')
            train_ratio: 训练集比例
            val_ratio: 验证集比例
            random_seed: 随机种子
        """
        self.data_folder = data_folder
        self.label_csv_path = label_csv_path
        self.normalize_ahi = normalize_ahi
        self.split = split
        
        # 加载数据文件列表
        self.parquet_files = self._load_file_list()
        
        # 加载AHI标签
        self.labels_df = pd.read_csv(label_csv_path)
        
        # 数据集分割
        self.parquet_files = self._split_dataset(
            self.parquet_files, split, train_ratio, val_ratio, random_seed
        )
        
        logger.info(f"加载 {split} 数据集: {len(self.parquet_files)} 个样本")
        
    def _load_file_list(self) -> List[str]:
        """加载parquet文件列表"""
        pattern = os.path.join(self.data_folder, "**", "*.parquet")
        files = glob(pattern, recursive=True)
        
        # 过滤掉统计文件
        files = [f for f in files if not f.endswith('_stats.csv') and not f.endswith('_label.csv')]
        
        logger.info(f"找到 {len(files)} 个数据文件")
        return files
    
    def _split_dataset(
        self, 
        files: List[str], 
        split: str, 
        train_ratio: float, 
        val_ratio: float, 
        random_seed: int
    ) -> List[str]:
        """分割数据集"""
        np.random.seed(random_seed)
        np.random.shuffle(files)
        
        n_total = len(files)
        n_train = int(n_total * train_ratio)
        n_val = int(n_total * val_ratio)
        
        if split == 'train':
            return files[:n_train]
        elif split == 'val':
            return files[n_train:n_train + n_val]
        elif split == 'test':
            return files[n_train + n_val:]
        else:
            raise ValueError(f"Unknown split: {split}")
    
    def _get_ahi_label(self, session_id: str) -> float:
        """获取AHI标签"""
        try:
            # 从session_id提取nsrrid
            nsrrid = int(session_id.split('-')[1])
            row = self.labels_df[self.labels_df['nsrrid'] == nsrrid]
            
            if len(row) > 0:
                ahi_value = row['ahi_a0h3'].iloc[0]
                
                # 归一化AHI值
                if self.normalize_ahi:
                    ahi_value = normalize_ahi_piecewise(ahi_value)
                
                return float(ahi_value)
            else:
                logger.warning(f"未找到 {session_id} 的AHI标签")
                return 0.0
                
        except Exception as e:
            logger.error(f"获取AHI标签失败 {session_id}: {e}")
            return 0.0
    
    def _load_signals(self, parquet_file: str) -> Dict[str, torch.Tensor]:
        """加载信号数据"""
        try:
            df = pd.read_parquet(parquet_file)
            signals = {}
            
            # 加载五个信号通道
            for signal_name in [ECG, SAO2, ABD, THX, AIRFLOW]:
                if signal_name in df.columns:
                    signal_data = df[signal_name].values
                    
                    # 处理NaN值
                    signal_data = np.nan_to_num(signal_data, nan=0.0)
                    
                    # 转换为tensor
                    signals[signal_name] = torch.tensor(signal_data, dtype=torch.float32)
                else:
                    # 如果信号不存在，创建零信号
                    if signal_name == ECG:
                        length = 387_789
                    else:
                        length = 96_947
                    signals[signal_name] = torch.zeros(length, dtype=torch.float32)
            
            return signals
            
        except Exception as e:
            logger.error(f"加载信号数据失败 {parquet_file}: {e}")
            # 返回零信号
            return {
                ECG: torch.zeros(387_789, dtype=torch.float32),
                SAO2: torch.zeros(96_947, dtype=torch.float32),
                ABD: torch.zeros(96_947, dtype=torch.float32),
                THX: torch.zeros(96_947, dtype=torch.float32),
                AIRFLOW: torch.zeros(96_947, dtype=torch.float32)
            }
    
    def __len__(self) -> int:
        return len(self.parquet_files)
    
    def __getitem__(self, idx: int) -> Tuple[Dict[str, torch.Tensor], torch.Tensor]:
        """获取单个样本"""
        parquet_file = self.parquet_files[idx]
        session_id = os.path.basename(parquet_file).replace('.parquet', '')
        
        # 加载信号数据
        signals = self._load_signals(parquet_file)
        
        # 获取AHI标签
        ahi_label = self._get_ahi_label(session_id)
        ahi_tensor = torch.tensor(ahi_label, dtype=torch.float32)
        
        return signals, ahi_tensor
    
    def get_sample_info(self, idx: int) -> Dict:
        """获取样本信息（用于调试）"""
        parquet_file = self.parquet_files[idx]
        session_id = os.path.basename(parquet_file).replace('.parquet', '')
        
        return {
            'session_id': session_id,
            'parquet_file': parquet_file,
            'ahi_label': self._get_ahi_label(session_id)
        }


def create_timesnet_dataloader(
    data_folder: str,
    label_csv_path: str = './shhs1_label.csv',
    batch_size: int = 8,
    split: str = 'train',
    normalize_ahi: bool = True,
    num_workers: int = 4,
    shuffle: bool = None
) -> DataLoader:
    """创建TimesNet数据加载器"""
    
    # 默认训练集shuffle，其他不shuffle
    if shuffle is None:
        shuffle = (split == 'train')
    
    dataset = TimesNetSleepDataset(
        data_folder=data_folder,
        label_csv_path=label_csv_path,
        normalize_ahi=normalize_ahi,
        split=split
    )
    
    dataloader = DataLoader(
        dataset,
        batch_size=batch_size,
        shuffle=shuffle,
        num_workers=num_workers,
        pin_memory=True,
        drop_last=(split == 'train')  # 训练时丢弃最后一个不完整的batch
    )
    
    return dataloader


def test_timesnet_dataset():
    """测试TimesNet数据集"""
    print("测试TimesNet数据集...")
    
    # 创建数据集
    dataset = TimesNetSleepDataset(
        data_folder="E:/OSA/shhs_processed",
        label_csv_path="./shhs1_label.csv",
        split='train'
    )
    
    print(f"数据集大小: {len(dataset)}")
    
    # 测试单个样本
    if len(dataset) > 0:
        signals, ahi_label = dataset[0]
        
        print(f"信号通道: {list(signals.keys())}")
        for name, signal in signals.items():
            print(f"  {name}: shape={signal.shape}, dtype={signal.dtype}")
        
        print(f"AHI标签: {ahi_label.item():.4f}")
        
        # 测试数据加载器
        dataloader = create_timesnet_dataloader(
            data_folder="E:/OSA/shhs_processed",
            batch_size=2,
            split='train'
        )
        
        batch_signals, batch_ahi = next(iter(dataloader))
        print(f"\nBatch测试:")
        print(f"Batch大小: {batch_ahi.shape[0]}")
        for name, signal in batch_signals.items():
            print(f"  {name}: shape={signal.shape}")
        print(f"AHI标签: shape={batch_ahi.shape}")


if __name__ == "__main__":
    test_timesnet_dataset()
