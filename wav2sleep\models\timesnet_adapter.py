"""TimesNet适配器，用于AHI回归任务"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import sys
import os

# 添加TimesNet路径到系统路径
timesnet_path = os.path.join(os.path.dirname(__file__), '../../config/TimesNet')
sys.path.insert(0, timesnet_path)

# 直接导入TimesNet代码，避免路径问题
import importlib.util

def load_timesnet_modules():
    """动态加载TimesNet模块"""
    try:
        # 加载embed.py
        embed_spec = importlib.util.spec_from_file_location(
            "timesnet_embed",
            os.path.join(timesnet_path, "embed.py")
        )
        embed_module = importlib.util.module_from_spec(embed_spec)
        embed_spec.loader.exec_module(embed_module)

        # 加载conv_block.py
        conv_spec = importlib.util.spec_from_file_location(
            "timesnet_conv",
            os.path.join(timesnet_path, "conv_block.py")
        )
        conv_module = importlib.util.module_from_spec(conv_spec)
        conv_spec.loader.exec_module(conv_module)

        # 将必要的类添加到全局命名空间
        globals()['DataEmbedding'] = embed_module.DataEmbedding
        globals()['Inception_Block_V1'] = conv_module.Inception_Block_V1

        # 将模块添加到sys.modules，这样models.py可以导入它们
        sys.modules['embed'] = embed_module
        sys.modules['conv_block'] = conv_module

        # 加载models.py
        models_spec = importlib.util.spec_from_file_location(
            "timesnet_models",
            os.path.join(timesnet_path, "models.py")
        )
        models_module = importlib.util.module_from_spec(models_spec)
        models_spec.loader.exec_module(models_module)

        return models_module.Model, embed_module.DataEmbedding

    except Exception as e:
        print(f"Failed to load TimesNet modules: {e}")
        return None, None

# 尝试加载TimesNet
TimesNetCore, DataEmbedding = load_timesnet_modules()
TIMESNET_AVAILABLE = (TimesNetCore is not None)

if TIMESNET_AVAILABLE:
    print("TimesNet loaded successfully!")
else:
    print("TimesNet loading failed, will use SimpleTimesNet only")


class TimesNetConfig:
    """TimesNet配置类，适配我们的AHI回归任务"""
    def __init__(self):
        # 基础配置
        self.task_name = 'long_term_forecast'  # 使用预测任务模式
        
        # 数据配置 - 优化后的维度
        self.seq_len = 5000        # 压缩后的序列长度（从96,947压缩到5000）
        self.label_len = 24        # 标签长度（相应减少）
        self.pred_len = 1          # 预测长度（输出1个AHI值）
        self.enc_in = 5            # 输入特征数（5个信号通道）
        self.c_out = 1             # 输出维度（AHI值）

        # 模型配置 - 适度减少复杂度
        self.d_model = 256         # 减少模型维度
        self.d_ff = 1024          # 减少前馈网络维度
        self.num_kernels = 4       # 减少Inception块的卷积核数量
        self.top_k = 3             # 减少FFT中选择的top频率数量
        self.e_layers = 2          # 减少编码器层数
        
        # 嵌入配置
        self.embed = 'timeF'       # 嵌入类型
        self.freq = 'h'            # 频率类型
        self.dropout = 0.1         # Dropout率


class MultiScaleSignalPreprocessor(nn.Module):
    """多尺度信号预处理器，智能压缩时序数据"""

    def __init__(self, target_length=5000, use_multiscale=True):
        super().__init__()
        self.target_length = target_length
        self.use_multiscale = use_multiscale

        # 多尺度下采样层
        if use_multiscale:
            # 不同尺度的特征提取
            self.scale1_pool = nn.AdaptiveAvgPool1d(target_length)      # 全局平均
            self.scale2_pool = nn.AdaptiveMaxPool1d(target_length)      # 全局最大
            self.scale3_conv = nn.Conv1d(1, 1, kernel_size=19, stride=19, padding=9)  # 卷积下采样

            # 特征融合
            self.feature_fusion = nn.Conv1d(3, 1, kernel_size=1)
        else:
            # 简单下采样
            self.simple_pool = nn.AdaptiveAvgPool1d(target_length)

        # 信号标准化
        self.signal_norm = nn.LayerNorm(target_length)

    def forward(self, x_dict):
        """
        多尺度信号预处理

        Args:
            x_dict: 字典格式 {'ECG': tensor, 'SAO2': tensor, ...}

        Returns:
            x: (batch_size, target_length, features) 格式的张量
        """
        batch_size = next(iter(x_dict.values())).size(0)
        device = next(iter(x_dict.values())).device
        processed_signals = []

        # 按固定顺序处理信号
        signal_order = ['ECG', 'SAO2', 'ABD', 'THX', 'AIRFLOW']

        for signal_name in signal_order:
            if signal_name in x_dict:
                signal = x_dict[signal_name]

                # 处理NaN值
                signal = torch.nan_to_num(signal, nan=0.0)

                # 多尺度处理
                if self.use_multiscale:
                    # 添加通道维度用于卷积
                    signal_expanded = signal.unsqueeze(1)  # (batch, 1, seq_len)

                    # 三种不同尺度的特征
                    scale1 = self.scale1_pool(signal_expanded)  # 平均池化
                    scale2 = self.scale2_pool(signal_expanded)  # 最大池化

                    # 卷积下采样（需要调整到目标长度）
                    scale3 = self.scale3_conv(signal_expanded)
                    if scale3.size(-1) != self.target_length:
                        scale3 = F.adaptive_avg_pool1d(scale3, self.target_length)

                    # 融合多尺度特征
                    multi_scale = torch.cat([scale1, scale2, scale3], dim=1)  # (batch, 3, target_length)
                    fused = self.feature_fusion(multi_scale).squeeze(1)  # (batch, target_length)
                else:
                    # 简单下采样
                    fused = self.simple_pool(signal.unsqueeze(1)).squeeze(1)

                # 信号标准化
                fused = self.signal_norm(fused)
                processed_signals.append(fused)
            else:
                # 如果信号不存在，用零填充
                processed_signals.append(torch.zeros(batch_size, self.target_length, device=device))

        # 堆叠成 (batch_size, target_length, features)
        x = torch.stack(processed_signals, dim=-1)
        return x


class TimesNetForAHI(nn.Module):
    """TimesNet适配器，专门用于AHI回归"""
    
    def __init__(self, configs=None):
        super().__init__()
        
        # 使用默认配置或传入的配置
        self.configs = configs if configs is not None else TimesNetConfig()
        
        # 信号预处理器 - 使用多尺度处理
        self.preprocessor = MultiScaleSignalPreprocessor(
            target_length=5000,  # 大幅压缩序列长度
            use_multiscale=True
        )
        
        # TimesNet核心模型
        self.timesnet = TimesNetCore(self.configs)
        
        # AHI回归头（替换TimesNet的预测层）
        self.ahi_regressor = nn.Sequential(
            nn.Linear(self.configs.seq_len, 512),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(512, 128),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(128, 1)
        )
        
    def forward(self, x_dict):
        """
        前向传播
        
        Args:
            x_dict: 字典格式的输入信号
            
        Returns:
            ahi_pred: AHI预测值 (batch_size,)
        """
        # 1. 预处理信号
        x = self.preprocessor(x_dict)  # (batch_size, seq_len, features)
        
        # 2. 创建时间标记（TimesNet需要，但对我们的任务不重要）
        batch_size, seq_len, _ = x.shape
        x_mark = torch.zeros(batch_size, seq_len, 1, device=x.device)
        
        # 3. 创建解码器输入（TimesNet预测模式需要）
        x_dec = torch.zeros(batch_size, self.configs.label_len + self.configs.pred_len, 
                           self.configs.enc_in, device=x.device)
        x_mark_dec = torch.zeros(batch_size, self.configs.label_len + self.configs.pred_len, 
                                1, device=x.device)
        
        # 4. TimesNet前向传播
        try:
            # 使用预测模式
            timesnet_out = self.timesnet.forecast(x, x_mark, x_dec, x_mark_dec)
            # timesnet_out shape: (batch_size, pred_len, c_out)
            
            # 5. 提取特征用于AHI回归
            # 我们使用TimesNet的内部表示而不是最终预测
            # 重新通过TimesNet获取编码特征
            enc_out = self.timesnet.enc_embedding(x, x_mark)
            for i in range(self.timesnet.layer):
                enc_out = self.timesnet.layer_norm(self.timesnet.model[i](enc_out))
            
            # 6. 全局平均池化 + AHI回归
            # enc_out shape: (batch_size, seq_len, d_model)
            global_features = enc_out.mean(dim=1)  # (batch_size, d_model)
            
            # 7. AHI预测
            ahi_pred = self.ahi_regressor(global_features).squeeze(-1)  # (batch_size,)
            
            return ahi_pred
            
        except Exception as e:
            print(f"TimesNet forward error: {e}")
            # 如果TimesNet出错，返回零预测
            return torch.zeros(batch_size, device=x.device)
    
    @property
    def valid_signals(self):
        """返回模型支持的信号列表"""
        return ['ECG', 'SAO2', 'ABD', 'THX', 'AIRFLOW']


class SimpleTimesNetForAHI(nn.Module):
    """简化版TimesNet，专门为AHI回归设计"""
    
    def __init__(self, d_model=256, num_layers=3):
        super().__init__()
        
        self.d_model = d_model
        self.preprocessor = MultiScaleSignalPreprocessor(target_length=5000)
        
        # 输入嵌入
        self.input_embedding = nn.Linear(5, d_model)  # 5个信号通道
        
        # 位置编码 - 适应新的序列长度
        self.pos_embedding = nn.Parameter(torch.randn(1, 5000, d_model))
        
        # 多尺度卷积层
        self.conv_layers = nn.ModuleList([
            nn.Conv1d(d_model, d_model, kernel_size=k, padding=k//2)
            for k in [3, 7, 15, 31]  # 不同时间尺度
        ])
        
        # 注意力层
        self.attention = nn.MultiheadAttention(d_model, num_heads=8, batch_first=True)
        
        # 层归一化
        self.layer_norms = nn.ModuleList([nn.LayerNorm(d_model) for _ in range(num_layers)])
        
        # AHI回归头
        self.regressor = nn.Sequential(
            nn.AdaptiveAvgPool1d(1),  # 全局平均池化
            nn.Flatten(),
            nn.Linear(d_model, 128),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(128, 1)
        )
        
    def forward(self, x_dict):
        # 预处理
        x = self.preprocessor(x_dict)  # (batch_size, seq_len, 5)
        
        # 输入嵌入
        x = self.input_embedding(x)  # (batch_size, seq_len, d_model)
        
        # 位置编码
        x = x + self.pos_embedding
        
        # 多尺度卷积
        conv_outs = []
        x_conv = x.transpose(1, 2)  # (batch_size, d_model, seq_len)
        for conv in self.conv_layers:
            conv_out = F.relu(conv(x_conv))
            conv_outs.append(conv_out)
        
        # 融合多尺度特征
        x = torch.stack(conv_outs, dim=0).mean(dim=0)  # (batch_size, d_model, seq_len)
        x = x.transpose(1, 2)  # (batch_size, seq_len, d_model)
        
        # 自注意力
        attn_out, _ = self.attention(x, x, x)
        x = self.layer_norms[0](x + attn_out)
        
        # AHI回归
        x = x.transpose(1, 2)  # (batch_size, d_model, seq_len)
        ahi_pred = self.regressor(x).squeeze(-1)  # (batch_size,)
        
        return ahi_pred
    
    @property
    def valid_signals(self):
        return ['ECG', 'SAO2', 'ABD', 'THX', 'AIRFLOW']
