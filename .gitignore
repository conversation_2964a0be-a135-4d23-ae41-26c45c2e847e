# Distribution / packaging
__pycache__/
*.py[cod]
*$py.class
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/
junit.xml

# Jupyter Notebook
.ipynb_checkpoints

# dotenv
.env

# Spyder project settings
.spyderproject
.spyproject

# IDE settings
.vscode/
.idea/

# Caches
.mypy_cache/
.pytest_cache/
.tox/

# Remove logs (*.log, log.txt) and rotated logs (log.txt.1)
*.log
**/log.txt*

# Hydra / MLFlow / etc.
mlruns
datasets
multirun
outputs
notebooks
