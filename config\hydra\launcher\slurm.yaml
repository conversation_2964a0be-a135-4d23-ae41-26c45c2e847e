defaults:
  - submitit_slurm

name: ${hydra.job.name}
nodes: 1
tasks_per_node: ${num_gpus}
cpus_per_task: ${num_cpus}
mem_gb: 96
timeout_min: 720

partition: short
qos: priority

constraint: 'gpu_mem:32GB|gpu_mem:40GB|gpu_mem:48GB'

additional_parameters:
  clusters: all

# Following parameters are submitit specifics
# # A list of commands to run in sbatch befure running srun
setup:
  - module load CUDA/11.8.0
  - module load cuDNN/8.7.0.84-CUDA-11.8.0

gres: "gpu:${num_gpus}"
cpus_per_gpu: null
gpus_per_node: null
mem_per_gpu: null
mem_per_cpu: null
account: null
comment: null
exclude: null
stderr_to_stdout: False
array_parallelism: 256
