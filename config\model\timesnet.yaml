# TimesNet模型配置文件，用于AHI回归任务

_target_: wav2sleep.models.timesnet_adapter.TimesNetForAHI

# TimesNet配置参数
configs:
  # 任务配置
  task_name: 'long_term_forecast'
  
  # 数据维度配置 - 优化后
  seq_len: 5000         # 压缩后的序列长度
  label_len: 24         # 标签长度（TimesNet需要）
  pred_len: 1           # 预测长度（输出1个AHI值）
  enc_in: 5             # 输入特征数（5个信号通道）
  c_out: 1              # 输出维度（AHI值）

  # 模型架构配置 - 轻量化
  d_model: 256          # 模型维度
  d_ff: 1024           # 前馈网络维度
  num_kernels: 4        # Inception块的卷积核数量
  top_k: 3              # FFT中选择的top频率数量
  e_layers: 2           # 编码器层数
  
  # 嵌入配置
  embed: 'timeF'        # 嵌入类型
  freq: 'h'             # 频率类型
  dropout: 0.1          # Dropout率

# 训练相关配置
training:
  loss_function: 'mse'  # 损失函数：mse, mae, huber
  optimizer: 'adam'
  learning_rate: 0.0001
  weight_decay: 0.0001
  
# 数据预处理配置
preprocessing:
  normalize_ahi: true   # 是否归一化AHI值
  handle_missing: 'zero'  # 处理缺失值的方法：zero, interpolate
  
# 模型输出配置
output:
  activation: 'none'    # 输出激活函数：none, sigmoid, relu
  clip_range: [0, 100]  # AHI值裁剪范围
