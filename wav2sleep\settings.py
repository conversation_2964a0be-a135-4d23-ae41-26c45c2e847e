# Output column names
PPG = 'PPG'
ECG = 'ECG'
ABD = 'ABD'
THX = 'THX'
AIRFLOW = 'AIRFLOW'
NEW_AIR = 'NEW_AIR'
SAO2 = 'SAO2'
LABEL = 'event_label'
TIMESTAMP = 'Timestamp'
SLEEP = 'Sleep'

# Mapping of signals to expected shape
HIGH_FREQ_LEN = 1_228_800
LOW_FREQ_LEN = 307_200
ULTRA_LOW_FREQ_LEN = 95
# 更新为实际清洗后的数据长度（基于实际观察到的长度）
ACTUAL_HIGH_FREQ_LEN = 388_042  # ECG实际长度
ACTUAL_LOW_FREQ_LEN = 97_010    # 其他信号实际长度

COL_MAP = {
    ECG: ACTUAL_HIGH_FREQ_LEN,
    SAO2: ACTUAL_LOW_FREQ_LEN,
    ABD: ACTUAL_LOW_FREQ_LEN,
    THX: ACTUAL_LOW_FREQ_LEN,
    AIRFLOW: ACTUAL_LOW_FREQ_LEN,
    # 保留原有定义以向后兼容
    PPG: HIGH_FREQ_LEN,
    NEW_AIR: LOW_FREQ_LEN
}

# PSG datasets
SHHS = 'shhs'
MESA = 'mesa'
CFS = 'cfs'
CHAT = 'chat'
CCSHS = 'ccshs'
MROS = 'mros'
WSC = 'wsc'

# AHI处理相关常量和函数
AHI_MAX_VALUE = 120.0  # AHI的最大合理值

def normalize_ahi(ahi: float) -> float:
    """使用原始的Z-score标准化方法"""
    return (ahi - AHI_MEAN) / AHI_STD

def denormalize_ahi(normalized_ahi):
    """将Z-score标准化的AHI值还原为原始值，支持张量和标量"""
    import torch

    # 处理张量和标量输入
    if torch.is_tensor(normalized_ahi):
        return normalized_ahi * AHI_STD + AHI_MEAN
    else:
        return normalized_ahi * AHI_STD + AHI_MEAN

def ahi_to_class(ahi):
    """将AHI值转换为分类标签 (0-3)，支持张量和标量"""
    import torch

    # 处理张量输入
    if torch.is_tensor(ahi):
        result = torch.zeros_like(ahi, dtype=torch.long)
        result[(ahi >= 5) & (ahi < 15)] = 1  # Mild
        result[(ahi >= 15) & (ahi < 30)] = 2  # Moderate
        result[ahi >= 30] = 3  # Severe
        return result
    else:
        # 处理标量输入
        if ahi < 5:
            return 0  # Normal
        elif ahi < 15:
            return 1  # Mild
        elif ahi < 30:
            return 2  # Moderate
        else:
            return 3  # Severe

# Folder for census-balanced dataset used by Jones et al. paper
CENSUS = 'census'

KNOWN_DATASETS = [SHHS, MESA, CFS, CHAT, CCSHS, MROS, WSC, CENSUS]

INGEST = 'ingest'  # Temporary folder for each dataset to store parquet before splitting into train/val/test.
TRAIN, VAL, TEST = 'train', 'val', 'test'

# Mappings from five class sleep stages to integers.
INTEGER_LABEL_MAPS = {
    4: {1: 0, 2: 1, 3: 2, 4: 3},
}
AHI_MAX_VALUE = 120
AHI_MEAN = 19.4668
AHI_STD = 18.5564
AHI_MAX_VALUE1=58.05