# Output column names
PPG = 'PPG'
ECG = 'ECG'
ABD = 'ABD'
THX = 'THX'
AIRFLOW = 'AIRFLOW'
NEW_AIR = 'NEW_AIR'
SAO2 = 'SAO2'
LABEL = 'event_label'
TIMESTAMP = 'Timestamp'
SLEEP = 'Sleep'

# Mapping of signals to expected shape
HIGH_FREQ_LEN = 1_228_800
LOW_FREQ_LEN = 307_200
ULTRA_LOW_FREQ_LEN = 95
COL_MAP = {ECG: HIGH_FREQ_LEN, PPG: HIGH_FREQ_LEN, ABD: LOW_FREQ_LEN, THX: LOW_FREQ_LEN, AIRFLOW: LOW_FREQ_LEN, NEW_AIR: LOW_FREQ_LEN, SAO2: ULTRA_LOW_FREQ_LEN}

# PSG datasets
SHHS = 'shhs'
MESA = 'mesa'
CFS = 'cfs'
CHAT = 'chat'
CCSHS = 'ccshs'
MROS = 'mros'
WSC = 'wsc'

# Folder for census-balanced dataset used by <PERSON> et al. paper
CENSUS = 'census'

KNOWN_DATASETS = [SHHS, MESA, CFS, CHAT, CCSHS, MROS, WSC, CENSUS]

INGEST = 'ingest'  # Temporary folder for each dataset to store parquet before splitting into train/val/test.
TRAIN, VAL, TEST = 'train', 'val', 'test'

# Mappings from five class sleep stages to integers.
INTEGER_LABEL_MAPS = {
    4: {1: 0, 2: 1, 3: 2, 4: 3},
}
AHI_MAX_VALUE = 120
AHI_MEAN = 19.4668
AHI_STD = 18.5564
AHI_MAX_VALUE1=58.05