import os
import re
import random
import pandas as pd
import shutil
from sklearn.model_selection import train_test_split

def split_dataset_with_csv(
    parquet_dir: str,
    csv_path: str,
    output_dir: str,
    ratios: dict = {'train': 0.7, 'val': 0.15, 'test': 0.15},
    seed: int = 42
):
    """最终优化版：完整处理数字转换和格式匹配"""
    
    # 1. 处理CSV数据
    print("正在加载并预处理分类信息...")
    try:
        df = pd.read_csv(csv_path)
        
        # 转换nsrrid为规范化的6位数字字符串
        df['nsrrid'] = pd.to_numeric(df['nsrrid'], errors='coerce')  # 转换非法值为NaN
        df = df.dropna(subset=['nsrrid'])  # 删除无效行
        df['nsrrid'] = df['nsrrid'].astype(int).astype(str).str.zfill(6)
        
        # 构建ID映射字典
        id_to_class = dict(zip(df['nsrrid'], df['classification']))
        print(f"有效分类条目数：{len(id_to_class)}")
        
    except Exception as e:
        print(f"CSV文件处理失败：{str(e)}")
        return

    # 2. 处理Parquet文件
    print("\n正在分类Parquet文件...")
    class_files = {}
    unmatched_files = []
    
    for filename in os.listdir(parquet_dir):
        if not filename.endswith(".parquet"):
            continue
        
        # 提取纯数字ID（新方案）
        try:
            # 移除前缀和后缀获取核心ID
            core_id = filename.replace("shhs1-", "").replace(".parquet", "").strip()
            # 验证是否为6位数字
            if not core_id.isdigit() or len(core_id) != 6:
                print(f"异常文件名格式：{filename}")
                continue
            file_id = core_id.zfill(6)
        except:
            print(f"文件名解析失败：{filename}")
            continue
        
        # 匹配分类
        if file_id in id_to_class:
            class_label = id_to_class[file_id]
            class_files.setdefault(class_label, []).append(os.path.join(parquet_dir, filename))
        else:
            unmatched_files.append(filename)

    # 3. 输出统计信息
    print(f"\n匹配统计:")
    print(f"总Parquet文件数: {len(os.listdir(parquet_dir))}")
    print(f"成功匹配文件数: {sum(len(v) for v in class_files.values())}")
    print(f"未匹配文件数: {len(unmatched_files)}")
    print(f"示例未匹配文件: {unmatched_files[:3]}")

    # 4. 数据集分割
    print("\n开始分层分割...")
    random.seed(seed)
    split_data = {'train': [], 'val': [], 'test': []}
    
    for class_label, files in class_files.items():
        random.shuffle(files)
        total = len(files)
        
        # 动态调整分割比例
        train_size = int(total * ratios['train'])
        val_size = int(total * ratios['val'])
        test_size = total - train_size - val_size
        
        # 保证最小数量
        if min(train_size, val_size, test_size) < 1:
            train_size = max(1, int(total * 0.8))
            val_size = max(1, int(total * 0.1))
            test_size = max(1, total - train_size - val_size)
        
        split_data['train'].extend(files[:train_size])
        split_data['val'].extend(files[train_size:train_size+val_size])
        split_data['test'].extend(files[train_size+val_size:])
    
    # 5. 保存结果
    print("\n正在复制文件...")
    for split in split_data:
        os.makedirs(os.path.join(output_dir, split), exist_ok=True)
        print(f"处理 {split} 集 ({len(split_data[split])} 个文件)")
        for src_path in split_data[split]:
            shutil.copy2(src_path, os.path.join(output_dir, split, os.path.basename(src_path)))
    
    print("\n最终分布：")
    for split in split_data:
        print(f"- {split}: {len(split_data[split])} 个文件")

if __name__ == "__main__":
    split_dataset_with_csv(
        parquet_dir=f'/mnt/sda/xly/shhs/ingest',
        csv_path=f'/mnt/sda/xly/wav2sleep/classified_info.csv',
        output_dir=f'/mnt/sda/xly/shhs',
        ratios={'train': 0.7, 'val': 0.15, 'test': 0.15},
        seed=42
    )