"""Process the NSRR datasets for multi-channel time series to AHI regression.

Turns EDF files into parquet files containing processed signals and AHI labels.
"""

import argparse
import logging
import os
from glob import glob
# import xml.etree.ElementTree as ET  # 不再需要XML解析
from scipy import signal
import numpy as np
import pandas as pd
import ray
from tqdm import tqdm

from wav2sleep.data.edf import load_edf_data
from wav2sleep.data.txt import parse_txt_annotations
from wav2sleep.data.utils import interpolate_index
from wav2sleep.data.xml import parse_xml_annotations
from wav2sleep.parallel import parallelise
from wav2sleep.settings import (
    ABD,
    AIRFLOW,
    CHAT,
    ECG,
    INGEST,
    MROS,
    NEW_AIR,
    SAO2,
    SHHS,
    THX,
    WSC,
)

logger = logging.getLogger(__name__)

# 定义需要处理的五个信号通道，用于多通道时序到AHI回归
EDF_COLS = [SAO2, ECG, ABD, THX, AIRFLOW]

MAX_LENGTH = 60 * 60 * 10  # Recording length in seconds (trimmed to 10h)
# 定义时间窗口参数（保留原有设置以保持数据一致性）
DELTA1 = 5  # 原用于呼吸事件前后的时间窗口（秒）
DELTA2 = 90  # 原用于血氧下降延迟的最大时间（秒）
EPOCH_LENGTH = DELTA1 + DELTA2  # 时间窗口长度：95秒

# 根据新的epoch长度调整索引
TARGET_LABEL_INDEX = pd.Index(np.arange(0, MAX_LENGTH + 1, EPOCH_LENGTH)[1:])
HIGH_FREQ_SIGNAL_INDEX = pd.Index(np.arange(0, MAX_LENGTH + 1e-9, EPOCH_LENGTH / 1024)[1:])  # ~ 34 Hz，10.78 Hz
LOW_FREQ_SIGNAL_INDEX = pd.Index(np.arange(0, MAX_LENGTH + 1e-9, EPOCH_LENGTH / 256)[1:])  # ~ 8 Hz，2.69 Hz

# 定义各信号的固定采样率（Hz）
SAMPLING_RATES = {
    ECG: 125.0,      # ECG信号采样率
    THX: 10.0,       # 胸腔呼吸信号采样率
    ABD: 10.0,       # 腹部呼吸信号采样率
    AIRFLOW: 10.0,   # 气流信号采样率
    NEW_AIR: 10.0,   # 备用气流信号采样率
    SAO2: 1.0        # 血氧饱和度采样率
}

def apply_ecg_filter(signal_data, sample_freq):
    """对ECG信号应用0.3-45Hz带通滤波器"""
    nyquist_freq = 0.5 * sample_freq
    
    # 确保截止频率在0到1之间
    low_cutoff = 0.3 / nyquist_freq
    high_cutoff = 45.0 / nyquist_freq
    
    # 如果采样率太低，导致高截止频率超过1，调整采样率或截止频率
    if high_cutoff >= 1.0:
        print(f"警告：采样频率 {sample_freq}Hz 太低，无法应用45Hz高通滤波。调整为可能的最大值。")
        high_cutoff = 0.95  # 设置为接近1但小于1的值
    
    if low_cutoff >= high_cutoff:
        print(f"警告：低截止频率 ({low_cutoff}) 大于等于高截止频率 ({high_cutoff})。使用默认滤波器。")
        # 使用合理的默认值
        low_cutoff = 0.1
        high_cutoff = 0.8
    
    # 确保截止频率在有效范围内
    low_cutoff = max(0.001, min(low_cutoff, 0.99))
    high_cutoff = max(low_cutoff + 0.001, min(high_cutoff, 0.99))
    
    print(f"应用带通滤波器：采样率={sample_freq}Hz, 低截止={low_cutoff:.4f}, 高截止={high_cutoff:.4f}")
    b, a = signal.butter(4, [low_cutoff, high_cutoff], btype='band')
    return signal.filtfilt(b, a, signal_data)

def apply_respiratory_filters(signal_data, sample_freq):
    """对呼吸信号应用巴特沃斯滤波器:
    1. 0.6Hz低通滤波器，阶数为4
    2. 0.2Hz高通滤波器，阶数为2，消除基线漂移
    """
    nyquist_freq = 0.5 * sample_freq
    
    # 先应用低通滤波
    low_pass_cutoff = 0.6 / nyquist_freq
    # 确保截止频率在有效范围内
    if low_pass_cutoff >= 1.0:
        print(f"警告：采样频率 {sample_freq}Hz 太低，无法应用0.6Hz低通滤波。调整为可能的最大值。")
        low_pass_cutoff = 0.95
    low_pass_cutoff = max(0.001, min(low_pass_cutoff, 0.99))
    
    print(f"应用低通滤波器：采样率={sample_freq}Hz, 截止频率={low_pass_cutoff:.4f}")
    b_low, a_low = signal.butter(4, low_pass_cutoff, btype='low')
    signal_data = signal.filtfilt(b_low, a_low, signal_data)
    
    # 再应用高通滤波
    high_pass_cutoff = 0.2 / nyquist_freq
    # 确保截止频率在有效范围内
    if high_pass_cutoff >= 1.0:
        print(f"警告：采样频率 {sample_freq}Hz 太低，无法应用0.2Hz高通滤波。调整为默认值。")
        high_pass_cutoff = 0.05  # 使用较小的值，因为是高通滤波
    high_pass_cutoff = max(0.001, min(high_pass_cutoff, 0.99))
    
    print(f"应用高通滤波器：采样率={sample_freq}Hz, 截止频率={high_pass_cutoff:.4f}")
    b_high, a_high = signal.butter(2, high_pass_cutoff, btype='high')
    return signal.filtfilt(b_high, a_high, signal_data)

def apply_spo2_filter(signal_data):
    """对血氧饱和度应用Delta过滤器:
    1. 去除不符合生理特征的异常值（例如突降到0）
    2. 保持70-100%的有效区间
    3. 用线性插值填充缺失值
    """
    # 保存原始索引，以便后面填充
    original_index = signal_data.index
    
    # 步骤1和2: 去除异常值，只保留70-100%范围内的值
    mask = (signal_data >= 70) & (signal_data <= 100)
    filtered_signal = signal_data[mask]
    
    # 步骤3: 线性插值填充缺失值
    # 如果过滤后数据为空，返回原始数据
    if len(filtered_signal) == 0:
        return signal_data
    
    # 创建新的Series用于填充，明确指定dtype为float64
    filled_signal = pd.Series(index=original_index, dtype='float64')
    filled_signal[filtered_signal.index] = filtered_signal
    
    # 线性插值填充缺失值
    filled_signal = filled_signal.interpolate(method='linear')
    
    # 处理前后可能无法插值的端点
    filled_signal = filled_signal.fillna(method='ffill').fillna(method='bfill')
    
    # 如果仍有NaN值（整个序列都是NaN的情况），使用中位数填充
    if filled_signal.isna().any():
        # 如果没有任何有效值，使用合理的默认值95填充
        default_value = 95
        filled_signal = filled_signal.fillna(default_value)
    
    return filled_signal

# 添加新的数据填充策略函数
def fill_missing_values(signal_data, method='interpolate_then_zero', valid_range=None):
    """填充缺失值，支持多种策略
    
    Parameters:
    -----------
    signal_data : pd.Series
        包含可能缺失值的信号数据
    method : str
        填充方法：'interpolate_then_zero' - 范围内插值，范围外零填充
                'interpolate_then_nan' - 范围内插值，范围外保持NaN
                'zero' - 全部零填充
                'mean' - 均值填充
    valid_range : tuple, optional
        有效值范围 (min, max)，用于判断是否在生理合理范围内
        
    Returns:
    --------
    pd.Series
        填充后的信号数据
    """
    if signal_data is None or len(signal_data) == 0:
        return signal_data
        
    # 复制以避免修改原始数据
    filled_data = signal_data.copy()
    
    # 如果指定了有效范围，将范围外的值标记为NaN
    if valid_range is not None:
        min_val, max_val = valid_range
        filled_data[(filled_data < min_val) | (filled_data > max_val)] = np.nan
    
    if method == 'interpolate_then_zero':
        # 首先在有效范围内进行插值
        filled_data = filled_data.interpolate(method='linear')
        # 然后使用零填充剩余的NaN值
        filled_data = filled_data.fillna(0)
    
    elif method == 'interpolate_then_nan':
        # 只在有效范围内进行插值，保持范围外的NaN不变
        filled_data = filled_data.interpolate(method='linear')
    
    elif method == 'zero':
        # 直接使用零填充所有NaN
        filled_data = filled_data.fillna(0)
    
    elif method == 'mean':
        # 计算非NaN值的均值，然后填充
        mean_value = filled_data.mean()
        if pd.isna(mean_value):  # 如果全为NaN，则使用0
            mean_value = 0
        filled_data = filled_data.fillna(mean_value)
    
    return filled_data

def process_edf(edf: pd.DataFrame):
    """Process dataframe of EDF data."""
    signals = []
    column_names = []  # 存储列名

    def _process_edf_column(col, target_index, is_ecg=False, is_resp=False, is_spo2=False):
        """Process signal column of EDF with appropriate filtering"""
        if col in edf:
            # 去除NaN值
            clean_data = edf[col].dropna()

            # 使用预定义的采样频率，而不是估计
            if col in SAMPLING_RATES:
                sample_freq = SAMPLING_RATES[col]
                print(f"使用{col}的固定采样频率: {sample_freq}Hz")
            else:
                # 如果找不到预定义频率，使用默认值
                sample_freq = 100.0
                print(f"警告: {col}没有预定义采样频率，使用默认值{sample_freq}Hz")

            # 应用相应的滤波器
            try:
                if is_ecg:
                    print(f"应用ECG带通滤波器（0.3-45Hz）到 {col} 通道")
                    clean_data_values = clean_data.values.astype(float)
                    filtered_values = apply_ecg_filter(clean_data_values, sample_freq)
                    clean_data = pd.Series(filtered_values, index=clean_data.index)
                elif is_resp:
                    print(f"应用呼吸信号巴特沃斯滤波器到 {col} 通道")
                    clean_data_values = clean_data.values.astype(float)
                    filtered_values = apply_respiratory_filters(clean_data_values, sample_freq)
                    clean_data = pd.Series(filtered_values, index=clean_data.index)
                elif is_spo2:
                    print(f"应用血氧Delta滤波器到 {col} 通道")
                    clean_data = apply_spo2_filter(clean_data)
            except Exception as e:
                print(f"滤波器应用失败，使用原始信号: {e}")

            # 重采样到目标索引
            resampled_wav = interpolate_index(clean_data, target_index)

            # -- 新的处理流程: 先归一化，后处理无效值 --

            # 1. 将生理范围之外的值标记为NaN
            #    这样后续计算均值和标准差时会忽略它们
            signal_to_normalize = resampled_wav.copy()
            if is_spo2:
                # 对于血氧信号，有效范围是70-100
                signal_to_normalize[(signal_to_normalize < 70) | (signal_to_normalize > 100)] = np.nan

            # 2. 对有效数据进行归一化 (忽略NaN)
            mean = signal_to_normalize.mean()
            std = signal_to_normalize.std()

            # 检查标准差是否为有效数值且大于0
            if pd.notna(std) and std > 0:
                normalized_wav = (signal_to_normalize - mean) / std
            else:
                print(f"警告: {col} 标准差为零或无有效数据，跳过标准化。")
                # 如果有均值，则只进行中心化；否则返回原样（可能全是NaN）
                normalized_wav = signal_to_normalize - mean if pd.notna(mean) else signal_to_normalize

            # 3. 最终结果已包含NaN，符合要求
            signals.append(normalized_wav)
            column_names.append(str(col))  # 确保列名是字符串

    # 应用各种滤波器处理不同信号
    _process_edf_column(ECG, HIGH_FREQ_SIGNAL_INDEX, is_ecg=True)
    _process_edf_column(ABD, LOW_FREQ_SIGNAL_INDEX, is_resp=True)
    _process_edf_column(THX, LOW_FREQ_SIGNAL_INDEX, is_resp=True)
    _process_edf_column(SAO2, LOW_FREQ_SIGNAL_INDEX, is_spo2=True)
    _process_edf_column(AIRFLOW, LOW_FREQ_SIGNAL_INDEX, is_resp=True)

    # 创建DataFrame并确保列名是字符串
    result_df = pd.concat(signals, axis=1)
    result_df.columns = column_names  # 使用字符串列名

    return result_df.astype(np.float32)


# 移除事件检测相关函数，不再需要
# def is_valid_hypopnea(h_event, desats, threshold=2.7):

def get_ahi_label(session_id: str, label_csv_path: str = './shhs1_label.csv') -> float:
    """从shhs1_label.csv获取对应session的ahi_a0h3标签"""
    try:
        # 使用相对路径读取标签文件
        df = pd.read_csv(label_csv_path)
        # 从session_id提取nsrrid，例如 shhs1-200001 -> 200001
        nsrrid = int(session_id.split('-')[1])
        row = df[df['nsrrid'] == nsrrid]
        if len(row) > 0:
            ahi_value = row['ahi_a0h3'].iloc[0]
            print(f"Session {session_id}: 找到AHI标签 = {ahi_value:.2f}")
            return float(ahi_value)
        else:
            logger.warning(f"Session {session_id}: 在{label_csv_path}中未找到对应的nsrrid={nsrrid}")
            return None
    except Exception as e:
        logger.error(f"读取AHI标签失败: {e}")
        return None

def process(edf_fp: str, output_fp: str, overwrite: bool = False, label_csv_path: str = './shhs1_label.csv') -> bool:
    """Process night of data for multi-channel time series to AHI regression."""
    if os.path.exists(output_fp) and not overwrite:
        logger.debug(f'Skipping {edf_fp=}, {output_fp=}, already exists')
        return False
    else:
        os.makedirs(os.path.dirname(output_fp), exist_ok=True)

    # 1. 获取session_id并读取对应的AHI标签
    session_id = os.path.basename(edf_fp).replace('.edf', '')
    ahi_label = get_ahi_label(session_id, label_csv_path)

    if ahi_label is None:
        logger.warning(f"跳过处理 {session_id}: 无法获取AHI标签")
        return False

    # 2. 简化处理：只需要信号数据，不需要事件检测
    print(f"开始处理信号数据，目标AHI标签: {ahi_label:.2f}")

    # 实现更健壮的信号加载策略
    try:
        # 首次尝试加载包含 AIRFLOW 的标准信号列表
        try_edf_cols = EDF_COLS.copy()
        print(f"尝试加载信号通道: {try_edf_cols}")
        edf = load_edf_data(edf_fp, columns=try_edf_cols)
    except (ValueError, KeyError) as e:
        logger.warning(f"加载标准信号通道失败: {e}")
        # 定义备用通道方案
        backup_plans = [
            # 备用方案1: 使用NEW_AIR替代AIRFLOW
            {AIRFLOW: NEW_AIR},
            # 备用方案2: 尝试不同的ECG通道名称
            {ECG: 'ECG1'},
            {ECG: 'EKG'},
            # 备用方案3: 尝试不同的呼吸信号通道名称
            {ABD: 'ABDO'},
            {THX: 'THOR'},
            {AIRFLOW: 'FLOW'},
            # 备用方案4: 尝试不同的血氧通道名称
            {SAO2: 'SpO2'},
            {SAO2: 'SaO2'},
        ]

        # 尝试各种备用方案
        loaded = False
        for plan in backup_plans:
            try:
                # 创建一个新的通道列表，替换原始通道
                alt_cols = try_edf_cols.copy()
                for orig_col, alt_col in plan.items():
                    if orig_col in alt_cols:
                        idx = alt_cols.index(orig_col)
                        alt_cols[idx] = alt_col

                print(f"尝试备用通道方案: {alt_cols}")
                edf = load_edf_data(edf_fp, columns=alt_cols)

                # 如果成功加载，将通道名称映射回原始名称
                for orig_col, alt_col in plan.items():
                    if alt_col in edf.columns:
                        edf = edf.rename(columns={alt_col: orig_col})

                loaded = True
                print(f"成功使用备用通道加载: {list(edf.columns)}")
                break
            except Exception as e2:
                print(f"备用通道加载失败: {e2}")
                continue

        if not loaded:
            # 如果所有方案都失败，尝试加载所有可用通道并选择必要的部分
            try:
                print("尝试加载所有可用通道...")
                edf_all = load_edf_data(edf_fp)
                print(f"可用通道: {list(edf_all.columns)}")

                # 尝试寻找合适的替代通道
                available_cols = list(edf_all.columns)
                found_cols = {}

                # 搜索每个所需通道的可能替代
                for required_col in EDF_COLS:
                    # 尝试精确匹配
                    if required_col in available_cols:
                        found_cols[required_col] = required_col
                    else:
                        # 尝试模糊匹配
                        for col in available_cols:
                            if required_col.lower() in col.lower() or col.lower() in required_col.lower():
                                found_cols[required_col] = col
                                break

                # 如果至少找到了一个通道，创建过滤后的DataFrame
                if found_cols:
                    print(f"找到以下通道映射: {found_cols}")
                    edf = edf_all[[found_cols.get(col, col) for col in found_cols.keys()]]

                    # 重命名列
                    edf = edf.rename(columns={v: k for k, v in found_cols.items()})
                else:
                    raise ValueError("无法找到任何所需通道的替代")

            except Exception as e3:
                logger.error(f"所有加载方案都失败，无法处理文件 {edf_fp}: {e3}")
                return False

    # 捕获任何其他异常
    except Exception as e:
        logger.error(f"加载EDF时发生未预期的错误: {e}")
        return False

    try:
        # 处理信号
        waveform_df = process_edf(edf)

        # 确保所有列名都是字符串
        waveform_df.columns = [str(col) for col in waveform_df.columns]

        # 打印DataFrame信息
        print(f"信号DataFrame形状: {waveform_df.shape}")
        print(f"信号DataFrame列名: {list(waveform_df.columns)}")
        print(f"对应的AHI标签值: {ahi_label}")

        # 检查信号列的NaN情况（这是预期的）
        signal_cols = list(waveform_df.columns)
        nan_cols = [col for col in signal_cols if waveform_df[col].isna().any()]
        if nan_cols:
            print(f"信息: 以下信号列包含预期的NaN值: {nan_cols}")

        # 保存信号数据到parquet文件（不包含AHI标签）
        waveform_df.to_parquet(output_fp)
        print(f"成功保存信号数据到: {output_fp}")

        # 单独保存AHI标签信息到CSV文件
        label_output_fp = output_fp.replace('.parquet', '_label.csv')
        label_data = {
            "session_id": [session_id],
            "ahi_a0h3": [ahi_label]
        }
        label_df = pd.DataFrame(label_data)
        label_df.to_csv(label_output_fp, index=False, encoding='utf-8-sig')
        print(f"成功保存AHI标签到: {label_output_fp}")

        # 生成统计信息
        stats_output_fp = output_fp.replace('.parquet', '_stats.csv')
        stats_data = {
            "session_id": [session_id],
            "ahi_a0h3": [ahi_label],
            "signal_shape": [str(waveform_df.shape)],
            "signal_columns": [str(signal_cols)],
            "ecg_length": [len(waveform_df['ECG'].dropna()) if 'ECG' in waveform_df.columns else 0],
            "sao2_length": [len(waveform_df['SAO2'].dropna()) if 'SAO2' in waveform_df.columns else 0],
            "abd_length": [len(waveform_df['ABD'].dropna()) if 'ABD' in waveform_df.columns else 0],
            "thx_length": [len(waveform_df['THX'].dropna()) if 'THX' in waveform_df.columns else 0],
            "airflow_length": [len(waveform_df['AIRFLOW'].dropna()) if 'AIRFLOW' in waveform_df.columns else 0]
        }
        stats_df = pd.DataFrame(stats_data)
        stats_df.to_csv(stats_output_fp, index=False, encoding='utf-8-sig')
        print(f"成功保存统计信息到: {stats_output_fp}")

        return True

    except Exception as e:
        logger.error(f"处理信号时发生错误: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False


def get_edf_path(session_id: str, dataset: str, folder: str):
    if dataset == SHHS:
        partition, _ = session_id.split('-')  # shhs1 or shhs2
        edf_fp = os.path.join(folder, 'polysomnography/edfs', partition, f'{session_id}.edf')
    elif dataset == MROS:
        _, partition, *_ = session_id.split('-')  # mros visit 1 or 2
        edf_fp = os.path.join(folder, 'polysomnography/edfs', partition, f'{session_id}.edf')
    elif dataset == CHAT:
        if 'nonrandomized' in session_id:  # e.g. chat-baseline-nonrandomized-xxxx
            partition = 'nonrandomized'
        else:
            partition = session_id.split('-')[1]  # e.g. chat-baseline-xxxx
        edf_fp = os.path.join(folder, 'polysomnography/edfs', partition, f'{session_id}.edf')
        fixed_edf_fp = edf_fp.replace('.edf', '_fixed.edf')
        # Check for existence of fixed EDF (physical maximum is 0.0 in some files and needed fixing.)
        if os.path.exists(fixed_edf_fp):
            edf_fp = fixed_edf_fp
    else:
        edf_fp = os.path.join(folder, 'polysomnography/edfs', f'{session_id}.edf')
    return edf_fp


def prepare_dataset(folder: str, output_folder: str, dataset: str):
    """Prepare dataset IO locations for parallel processing."""
    fp_dict = {}

    # 直接从EDF文件列表构建处理字典，不再需要标注文件
    if dataset == WSC:
        edf_fps = glob(f'{folder}/polysomnography/*.edf', recursive=True)
        for edf_fp in edf_fps:
            if os.path.exists(edf_fp):
                session_id = os.path.basename(edf_fp).replace('.edf', '')
                output_fp = os.path.join(output_folder, dataset, INGEST, f'{session_id}.parquet')
                fp_dict[session_id] = {'edf_fp': edf_fp, 'output_fp': output_fp}
        return fp_dict

    # 对于其他数据集，从EDF文件夹直接获取文件列表
    if dataset == SHHS:
        # SHHS数据集的EDF文件在子文件夹中，例如: E:\OSA\shhs\polysomnography\edfs\shhs1\*.edf
        edf_fps = []
        for subfolder in ['shhs1', 'shhs2']:
            pattern = f'{folder}/polysomnography/edfs/{subfolder}/*.edf'
            found_files = glob(pattern)
            edf_fps.extend(found_files)
            print(f"在 {pattern} 中找到 {len(found_files)} 个EDF文件")
    else:
        # 其他数据集的通用模式
        edf_fps = glob(f'{folder}/polysomnography/edfs/**/*.edf', recursive=True)

    for edf_fp in edf_fps:
        if os.path.exists(edf_fp):
            session_id = os.path.basename(edf_fp).replace('.edf', '')

            # 根据session_id判断是shhs1还是shhs2，用于创建独立的输出文件夹
            if dataset == SHHS:
                output_subfolder = session_id.split('-')[0]
            else:
                output_subfolder = dataset

            output_fp = os.path.join(output_folder, output_subfolder, INGEST, f'{session_id}.parquet')
            fp_dict[session_id] = {'edf_fp': edf_fp, 'output_fp': output_fp}

    return fp_dict


def load_sample_for_training(parquet_file_path: str, label_csv_path: str = './shhs1_label.csv'):
    """
    加载单个样本用于训练，返回信号字典和AHI标签

    Args:
        parquet_file_path: 信号数据的parquet文件路径
        label_csv_path: 包含AHI标签的CSV文件路径

    Returns:
        signals: dict, 包含各个信号的numpy数组
        ahi_label: float, 对应的AHI标签值
    """
    # 加载信号数据
    signals_df = pd.read_parquet(parquet_file_path)

    # 提取session_id
    session_id = os.path.basename(parquet_file_path).replace('.parquet', '')

    # 从CSV文件获取AHI标签
    ahi_label = get_ahi_label(session_id, label_csv_path)

    # 将信号转换为字典格式，移除NaN值
    signals = {}
    for col in signals_df.columns:
        signal_data = signals_df[col].dropna().values
        signals[col] = signal_data
        print(f"{col}: {len(signal_data)} 个有效数据点")

    print(f"样本 {session_id}: AHI = {ahi_label}")
    return signals, ahi_label


def create_training_dataset_info(processed_folder: str, output_csv: str = 'training_dataset_info.csv'):
    """
    创建训练数据集的信息文件，包含所有样本的路径和标签信息

    Args:
        processed_folder: 处理后数据的文件夹路径
        output_csv: 输出的数据集信息CSV文件路径
    """
    parquet_files = glob(f"{processed_folder}/**/*.parquet", recursive=True)

    dataset_info = []
    for parquet_file in parquet_files:
        session_id = os.path.basename(parquet_file).replace('.parquet', '')

        # 获取AHI标签
        ahi_label = get_ahi_label(session_id)

        if ahi_label is not None:
            # 获取信号长度信息
            try:
                signals_df = pd.read_parquet(parquet_file)
                signal_lengths = {}
                for col in signals_df.columns:
                    signal_lengths[f"{col}_length"] = len(signals_df[col].dropna())

                info = {
                    'session_id': session_id,
                    'parquet_path': parquet_file,
                    'ahi_a0h3': ahi_label,
                    **signal_lengths
                }
                dataset_info.append(info)
            except Exception as e:
                print(f"处理文件 {parquet_file} 时出错: {e}")

    # 保存数据集信息
    dataset_df = pd.DataFrame(dataset_info)
    dataset_df.to_csv(output_csv, index=False, encoding='utf-8-sig')
    print(f"数据集信息已保存到: {output_csv}")
    print(f"总共 {len(dataset_df)} 个有效样本")

    # 打印统计信息
    print(f"\nAHI分布统计:")
    print(f"最小值: {dataset_df['ahi_a0h3'].min():.2f}")
    print(f"最大值: {dataset_df['ahi_a0h3'].max():.2f}")
    print(f"平均值: {dataset_df['ahi_a0h3'].mean():.2f}")
    print(f"中位数: {dataset_df['ahi_a0h3'].median():.2f}")

    return dataset_df


def validate_processed_data(processed_folder: str, sample_size: int = 5):
    """
    验证处理后的数据质量

    Args:
        processed_folder: 处理后数据的文件夹路径
        sample_size: 验证的样本数量
    """
    print(f"\n开始验证处理后的数据质量...")

    parquet_files = glob(f"{processed_folder}/**/*.parquet", recursive=True)

    if len(parquet_files) == 0:
        print("错误: 没有找到任何parquet文件")
        return False

    # 随机选择几个文件进行验证
    import random
    sample_files = random.sample(parquet_files, min(sample_size, len(parquet_files)))

    validation_results = []

    for parquet_file in sample_files:
        try:
            session_id = os.path.basename(parquet_file).replace('.parquet', '')
            print(f"\n验证样本: {session_id}")

            # 加载信号和标签
            signals, ahi_label = load_sample_for_training(parquet_file)

            # 验证信号数据
            signal_validation = {}
            for signal_name, signal_data in signals.items():
                signal_validation[signal_name] = {
                    'length': len(signal_data),
                    'has_nan': np.isnan(signal_data).any(),
                    'has_inf': np.isinf(signal_data).any(),
                    'mean': np.mean(signal_data),
                    'std': np.std(signal_data),
                    'min': np.min(signal_data),
                    'max': np.max(signal_data)
                }

                print(f"  {signal_name}: 长度={len(signal_data)}, 均值={np.mean(signal_data):.3f}, 标准差={np.std(signal_data):.3f}")

            # 验证AHI标签
            ahi_validation = {
                'ahi_value': ahi_label,
                'is_valid': ahi_label is not None and not np.isnan(ahi_label),
                'in_reasonable_range': 0 <= ahi_label <= 200 if ahi_label is not None else False
            }

            print(f"  AHI标签: {ahi_label} (有效: {ahi_validation['is_valid']})")

            validation_results.append({
                'session_id': session_id,
                'signals': signal_validation,
                'ahi': ahi_validation,
                'overall_valid': ahi_validation['is_valid'] and len(signals) > 0
            })

        except Exception as e:
            print(f"  验证失败: {e}")
            validation_results.append({
                'session_id': session_id,
                'error': str(e),
                'overall_valid': False
            })

    # 汇总验证结果
    valid_samples = sum(1 for r in validation_results if r.get('overall_valid', False))
    print(f"\n验证结果汇总:")
    print(f"验证样本数: {len(validation_results)}")
    print(f"有效样本数: {valid_samples}")
    print(f"有效率: {valid_samples/len(validation_results)*100:.1f}%")

    return valid_samples == len(validation_results)


def process_files(
    fp_dict: dict[str, dict[str, str]], max_parallel: int = 1, overwrite: bool = False, address: str = 'local'
):
    print(f'Preparing to process {len(fp_dict)} files.')

    def proc(arg_dict):
        try:
            return process(overwrite=overwrite, **arg_dict)
        except Exception as e:
            logger.error(f'Failed on {arg_dict} - {e}')
            print(f'Failed on {arg_dict} - {e}')
            return False

    if max_parallel > 1:
        # MEM_GB = int(os.environ.get('SLURM_MEM_PER_NODE', 8 * 1024)) / 1024  # SLURM env var. is in MB. Removing this as it can cause issues on Windows.
        ray.init(
            address=address,
            num_cpus=max_parallel,
            # object_store_memory=MEM_GB * 1024**3, # Let Ray manage memory automatically.
            ignore_reinit_error=True,
            include_dashboard=False,
        )
        num_converted = sum(parallelise(proc, fp_dict.values(), use_tqdm=True, max_parallel=max_parallel))
    else:
        num_converted = 0
        for fp_map in tqdm(fp_dict.values()):
            num_converted += process(**fp_map)
    print(f'Converted {num_converted} files.')


def parse_args():
    parser = argparse.ArgumentParser(prog='Dataset Processor', description='Process dataset.')
    parser.add_argument('--folder', help='Location of dataset.')
    parser.add_argument('--max-parallel', default=1, type=int, help='Parallel processes.')
    parser.add_argument('--cluster-address', default='local', type=str, help='Ray cluster address (defaults to local).')
    parser.add_argument('--output-folder', required=True, help='Base output folder for processed datasets.')
    parser.add_argument(
        '--overwrite',
        action='store_true',
        help='Overwrite existing parquet files.',
        default=False,
    )
    return parser.parse_args()


def main() -> None:
    # ====== 手动设置参数，直接运行 ======
    class Args:
        folder = r"E:\OSA\shhs"  # 数据集路径
        output_folder = r"E:\OSA\shhs_processed"  # 输出路径
        max_parallel = 1  # 禁用并行处理
        cluster_address = "local"
        overwrite = False  # 是否覆盖已存在的文件

    # 直接使用预设参数，不解析命令行
    args = Args()

    print(f"数据集路径: {args.folder}")
    print(f"输出路径: {args.output_folder}")
    print(f"覆盖模式: {args.overwrite}")

    # 检查数据集路径是否存在
    if not os.path.exists(args.folder):
        print(f"错误: 数据集路径不存在: {args.folder}")
        return

    # 检查标签文件是否存在
    label_csv_path = './shhs1_label.csv'
    if not os.path.exists(label_csv_path):
        print(f"错误: 标签文件不存在: {label_csv_path}")
        return

    # 由于Ray在Windows上可能不稳定，我们默认禁用并行，除非用户明确指定
    if args.max_parallel > 1:
        print(f"用户指定并行处理数: {args.max_parallel}")
    else:
        args.max_parallel = 1
        print("已禁用并行处理，将以单线程模式运行。")
    # =====================================

    # 确保输出目录存在
    os.makedirs(args.output_folder, exist_ok=True)

    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(os.path.join(args.output_folder, "processing_log.txt")),
            logging.StreamHandler()
        ]
    )

    # 记录处理开始信息
    logger.info(f"开始处理数据集: {args.folder}")
    logger.info(f"输出目录: {args.output_folder}")
    logger.info(f"并行度: {args.max_parallel}")
    logger.info(f"覆盖现有文件: {args.overwrite}")

    # 推断数据集名称
    dataset = os.path.basename(args.folder)  # 例如 path/to/mros
    print(f'Processing {dataset=}...')

    # 准备处理
    fp_dict = prepare_dataset(folder=args.folder, output_folder=args.output_folder, dataset=dataset)
    logger.info(f"找到 {len(fp_dict)} 个文件等待处理")

    # 处理文件
    process_files(fp_dict, max_parallel=args.max_parallel, overwrite=args.overwrite, address=args.cluster_address)

    # 生成训练数据集信息文件
    print("\n正在生成训练数据集信息...")
    dataset_info_path = os.path.join(args.output_folder, 'training_dataset_info.csv')
    try:
        dataset_df = create_training_dataset_info(args.output_folder, dataset_info_path)
        logger.info(f"训练数据集信息已生成: {dataset_info_path}")
        logger.info(f"有效样本数量: {len(dataset_df)}")
    except Exception as e:
        logger.error(f"生成训练数据集信息失败: {e}")

    # 验证处理后的数据质量
    print("\n正在验证数据质量...")
    try:
        validation_passed = validate_processed_data(args.output_folder, sample_size=5)
        if validation_passed:
            logger.info("数据质量验证通过")
            print("✅ 数据质量验证通过")
        else:
            logger.warning("数据质量验证发现问题")
            print("⚠️ 数据质量验证发现问题，请检查日志")
    except Exception as e:
        logger.error(f"数据质量验证失败: {e}")
        print(f"❌ 数据质量验证失败: {e}")

    # 记录处理完成信息
    logger.info(f"数据集处理完成: {args.folder}")
    print(f"\n🎉 数据清洗完成！")
    print(f"📁 信号数据保存在: {args.output_folder}")
    print(f"📊 训练数据集信息: {dataset_info_path}")
    print(f"🚀 现在可以使用这些数据进行模型训练了。")

    # 输出使用示例
    print(f"\n📝 使用示例:")
    print(f"```python")
    print(f"# 加载单个样本")
    print(f"from {os.path.basename(__file__).replace('.py', '')} import load_sample_for_training")
    print(f"signals, ahi_label = load_sample_for_training('path/to/sample.parquet')")
    print(f"")
    print(f"# 加载数据集信息")
    print(f"import pandas as pd")
    print(f"dataset_info = pd.read_csv('{dataset_info_path}')")
    print(f"```")

if __name__ == '__main__':
    main()
