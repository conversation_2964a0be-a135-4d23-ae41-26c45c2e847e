# 希尔伯特降维AHI预测模型配置

_target_: wav2sleep.models.hilbert_ahi_adapter.HilbertAHIPredictor

# 希尔伯特映射参数
ecg_output_dim: 128        # ECG信号降维后的维度
other_output_dim: 64       # 其他信号降维后的维度
gamma: 0.01               # RBF核参数，控制频率分布

# 回归网络参数
hidden_dim: 256           # 隐藏层维度
dropout: 0.3              # Dropout率

# 训练配置
training:
  loss_function: 'huber'   # 损失函数：mse, mae, huber
  optimizer: 'adamw'
  learning_rate: 0.001     # 较高的学习率，因为模型较小
  weight_decay: 0.01
  
# 数据配置
data:
  normalize_ahi: true
  ahi_normalization_method: 'piecewise'
  
# 优化配置
optimization:
  use_residual: true       # 是否使用残差连接
  optimize_for_inference: true  # 训练后是否优化推理
  
# 评估配置
evaluation:
  metrics: ['mae', 'rmse', 'mape', 'r2', 'pearson']
  classification_thresholds: [5, 15, 30]
