import os
import pandas as pd
import numpy as np
from tqdm import tqdm

train_dir = '/mnt/sda/xly/shhs/train'
max_files = 1956
ahi_candidates = ['ahi', 'AHI', 'ahi_index', 'apnea_hypopnea_index']
error_log_file = 'read_errors.log'
ahi_values = []

def find_ahi_column(columns):
    for candidate in ahi_candidates:
        if candidate in columns:
            return candidate
    return None

parquet_files = [f for f in os.listdir(train_dir) if f.endswith('.parquet')][:max_files]

with open(error_log_file, 'w') as log_file:
    for file in tqdm(parquet_files, desc='读取 Parquet 文件'):
        file_path = os.path.join(train_dir, file)
        try:
            df = pd.read_parquet(file_path)
            col = find_ahi_column(df.columns)
            if col:
                ahi = df[col].dropna().values
                ahi_values.extend(ahi)
            else:
                log_file.write(f"⚠️ 未找到 AHI 列：{file_path}\n")
        except Exception as e:
            log_file.write(f"读取失败：{file_path}\n错误信息：{str(e)}\n\n")

# 输出结果
if ahi_values:
    ahi_values = np.array(ahi_values)
    q95 = np.percentile(ahi_values, 95)
    print(f"\n✅ 成功读取 {len(ahi_values)} 个 AHI 值")
    print(f"📊 AHI 第 95 百分位数为：{q95:.2f}")
    print(f"⚠️ AHI 值 > {q95:.2f} 的异常数量：{np.sum(ahi_values > q95)}")
else:
    print("\n❌ 未成功读取任何 AHI 值，请检查 parquet 文件格式或列名是否匹配。")

print(f"\n📝 异常日志保存在：{os.path.abspath(error_log_file)}")




