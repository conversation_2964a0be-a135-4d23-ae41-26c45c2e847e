# 简化版TimesNet模型配置文件，专门为AHI回归设计

_target_: wav2sleep.models.timesnet_adapter.SimpleTimesNetForAHI

# 模型参数
d_model: 256          # 模型维度
num_layers: 3         # 层数

# 训练配置
training:
  loss_function: 'huber'  # 损失函数：mse, mae, huber
  optimizer: 'adamw'
  learning_rate: 0.0001
  weight_decay: 0.01
  
# 数据配置
data:
  normalize_ahi: true
  ahi_normalization_method: 'piecewise'  # 分段归一化
  
# 评估配置
evaluation:
  metrics: ['mae', 'rmse', 'mape', 'r2', 'pearson']
  classification_thresholds: [5, 15, 30]  # AHI分类阈值
