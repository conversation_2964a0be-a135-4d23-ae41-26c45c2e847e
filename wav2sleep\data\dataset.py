"""PyTorch dataset."""
import logging
import os
import pandas as pd
import torch
from torch import Tensor
from torch.utils.data import Dataset
import numpy as np

from ..settings import COL_MAP, INTEGER_LABEL_MAPS, LABEL, normalize_ahi

logger = logging.getLogger(__name__)
    
class ParquetDataset(Dataset):
    def __init__(
        self,
        parquet_fps: list[str],
        columns: list[str],
    ):
        self.files = parquet_fps
        self.columns = columns
        # 验证列名有效性
        for col in self.columns:
            if col not in COL_MAP:
                raise ValueError(f'Column {col} unrecognised.')

    def __getitem__(self, idx) -> tuple[dict[str, Tensor], Tensor]:
        fp = self.files[idx]
        df = try_read_parquet(fp)
        signal_dict = {}
        
        # 信号处理保持不变
        found_col = False
        for col in self.columns:
            sig_len = COL_MAP[col]
            if col in df.columns:
                actual_signal = torch.from_numpy(df[col].dropna().values).float()
                expected_len = sig_len
                actual_len = len(actual_signal)

                # 处理长度不匹配
                if actual_len != expected_len:
                    # 如果实际长度小于期望，用NaN填充；如果大于期望，截断
                    if actual_len < expected_len:
                        padded_signal = torch.full((expected_len,), float('nan'))
                        padded_signal[:actual_len] = actual_signal
                        signal_dict[col] = padded_signal
                    else:
                        signal_dict[col] = actual_signal[:expected_len]
                else:
                    signal_dict[col] = actual_signal
                found_col = True
            else:
                signal_dict[col] = torch.full((sig_len,), float('-inf')).float()
        if not found_col:
            raise ValueError(f'No relevant columns found in {fp=}. {self.columns=}')
            
        # ==== 修改：读取AHI回归标签 ====
        try:
            # 方案1：尝试从parquet文件中读取AHI标签（如果存在）
            if 'ahi_a0h3' in df.columns:
                ahi_value = df['ahi_a0h3'].iloc[0].item()
            else:
                # 方案2：从单独的label文件读取AHI标签
                label_file = fp.replace('.parquet', '_label.csv')
                if os.path.exists(label_file):
                    label_df = pd.read_csv(label_file)
                    ahi_value = label_df['ahi_a0h3'].iloc[0]
                else:
                    # 方案3：从原始CSV文件读取（需要session_id）
                    session_id = os.path.basename(fp).replace('.parquet', '')
                    # 尝试从全局CSV文件读取
                    try:
                        # 尝试多个可能的标签文件位置
                        possible_label_files = [
                            './shhs1_label.csv',  # 当前目录
                            'shhs1_label.csv',    # 相对路径
                            os.path.join(os.path.dirname(os.path.dirname(fp)), 'shhs1_label.csv'),  # 数据目录
                        ]

                        label_df = None
                        for global_label_file in possible_label_files:
                            if os.path.exists(global_label_file):
                                label_df = pd.read_csv(global_label_file)
                                break

                        if label_df is not None:
                            nsrrid = int(session_id.split('-')[1])
                            row = label_df[label_df['nsrrid'] == nsrrid]
                            if len(row) > 0:
                                ahi_value = row['ahi_a0h3'].iloc[0]
                            else:
                                raise ValueError(f"在标签文件中未找到nsrrid={nsrrid}")
                        else:
                            raise FileNotFoundError(f"无法找到任何AHI标签文件，尝试了: {possible_label_files}")
                    except Exception as e:
                        raise FileNotFoundError(f"无法获取AHI标签: {str(e)}")

            # 验证AHI值的合理性
            if not (0 <= ahi_value <= 200):
                logger.warning(f"AHI值超出合理范围: {ahi_value}")

            # 归一化AHI值
            normalized_ahi = normalize_ahi(ahi_value)
            label = torch.tensor(normalized_ahi, dtype=torch.float32)

        except Exception as e:
            raise RuntimeError(f"处理文件 {fp} 的AHI标签时出错: {str(e)}")

        return signal_dict, label

    def __len__(self) -> int:
        return len(self.files)


def try_read_parquet(fp: str, columns: list[str] | None = None, max_retries: int = 3):
    """Read parquet with retries for flaky filesystems."""
    try:
        return pd.read_parquet(fp, columns=columns)
    except Exception as e:
        logger.error(f'Failed to read parquet {fp=} - {e}')
        if max_retries > 0:
            return try_read_parquet(fp, columns=columns, max_retries=max_retries - 1)
        else:
            raise ValueError(f'Failed to read parquet {fp=}')                  