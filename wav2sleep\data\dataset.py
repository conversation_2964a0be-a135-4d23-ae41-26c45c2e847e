"""PyTorch dataset."""
import logging
from ..settings import AHI_MEAN, AHI_STD 
import pandas as pd
import torch
from torch import Tensor
from torch.utils.data import Dataset
import numpy as np

from ..settings import COL_MAP, INTEGER_LABEL_MAPS, LABEL, AHI_MAX_VALUE  # 新增AHI_MAX_VALUE导入

logger = logging.getLogger(__name__)

def normalize_ahi(ahi: float) -> float:
    """分段归一化AHI值
    [0,5] -> [0.0, 0.25]
    (5,15] -> (0.25, 0.50]
    (15,30] -> (0.50, 0.75]
    (30, AHI_MAX_VALUE] -> (0.75, 1.0]
    """
    ahi = max(min(ahi, AHI_MAX_VALUE), 0.0)
    
    if ahi <= 5:
        return ahi / 20.0  # 0-5 → 0-0.25
    elif ahi <= 15:
        return (ahi - 5) / 40.0 + 0.25  # 5-15 → 0.25-0.5
    elif ahi <= 30:
        return (ahi - 15) / 60.0 + 0.5  # 15-30 → 0.5-0.75
    else:
        return (ahi - 30) / (AHI_MAX_VALUE - 30) * 0.25 + 0.75  # 30-MAX → 0.75-1.0
    
class ParquetDataset(Dataset):
    def __init__(
        self,
        parquet_fps: list[str],
        columns: list[str],
    ):
        self.files = parquet_fps
        self.columns = columns
        # 验证列名有效性
        for col in self.columns:
            if col not in COL_MAP:
                raise ValueError(f'Column {col} unrecognised.')

    def __getitem__(self, idx) -> tuple[dict[str, Tensor], Tensor]:
        fp = self.files[idx]
        df = try_read_parquet(fp)
        signal_dict = {}
        
        # 信号处理保持不变
        found_col = False
        for col in self.columns:
            sig_len = COL_MAP[col]
            if col in df.columns:
                signal_dict[col] = torch.from_numpy(df[col].dropna().values).float()
                found_col = True
            else:
                signal_dict[col] = torch.full((sig_len,), float('-inf')).float()
        if not found_col:
            raise ValueError(f'No relevant columns found in {fp=}. {self.columns=}')
            
        # ==== 关键修改：读取AHI数值 ====
        try:
            # 假设标签列名为'event_label'，且值为0或1
            event_label = df[LABEL].iloc[0].item()
            
            # 验证标签值
            if event_label not in [0, 1]:
                raise ValueError(f"无效的事件标签值: {event_label} (应为0或1)")
                
            label = torch.tensor(event_label, dtype=torch.float32).squeeze()
        except Exception as e:
            raise RuntimeError(f"处理文件 {fp} 时出错: {str(e)}")

        return signal_dict, label

    def __len__(self) -> int:
        return len(self.files)


def try_read_parquet(fp: str, columns: list[str] | None = None, max_retries: int = 3):
    """Read parquet with retries for flaky filesystems."""
    try:
        return pd.read_parquet(fp, columns=columns)
    except Exception as e:
        logger.error(f'Failed to read parquet {fp=} - {e}')
        if max_retries > 0:
            return try_read_parquet(fp, columns=columns, max_retries=max_retries - 1)
        else:
            raise ValueError(f'Failed to read parquet {fp=}')                  