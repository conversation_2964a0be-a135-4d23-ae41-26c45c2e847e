# Main Hydra configuration file.
defaults:
  - /hydra/callbacks:
      - git
      - mlflow
      - timer
  - debug: none
  - model: wav2sleep
  - datasets: all
  - inputs: all
  - training: main
  - override /hydra/job_logging: colorlog
  - override /hydra/hydra_logging: colorlog
  - _self_

# Name of the job
name: ???

# Model configuration
feature_dim: 128

# Evaluation configuration
test: False # Run on the test sets after training.
restore_best: True # Restore lowest val. loss model for evaluation.

# Data config
data_location: "${oc.env:WAV2SLEEP_DATA}"
experiment: wav2sleep # mlflow experiment name
mlflow_experiment: "${oc.select:debug.experiment_override, ${experiment}}" # Log to debug experiment if in debug mode.

# Training configuration
num_classes: 4
seed: 42
target_batch_size: 16
batch_size: 4
tune_batch_size: False
epochs: 50
# ckpt_path: /root/medical data/wav2sleep/checkpoints/wav2sleep/wav2sleep_state_dict.pth # Path to a checkpoint file for resuming training.
ckpt_path: null
num_gpus: 1
num_cpus: 4

# Access overrides from main application for logging.
overrides: ${hydra:overrides}

hydra:
  verbose: ${debug.verbose}
  job_logging:
    handlers:
      file:
        filename:
          ${hydra.runtime.output_dir}/${hydra.job.name}_${oc.select:hydra.job.num, 0}.log
  run:
    dir: ${oc.env:WAV2SLEEP_STORAGE}/logs/${oc.select:debug.log_subfolder,.}/${hydra.job.name}/${now:%Y-%m-%d}/${now:%H-%M-%S}
  sweep:
    dir:  ${oc.env:WAV2SLEEP_STORAGE}/logs/${oc.select:debug.log_subfolder,.}/${hydra.job.name}/${now:%Y-%m-%d}/${now:%H-%M-%S}
    subdir: ${hydra.job.num}
