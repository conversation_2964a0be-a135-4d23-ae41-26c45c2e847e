"""Process the NSRR datasets.

Turns EDF files into parquet files containing sleep labels and the signals used.
"""

import argparse
import logging
import os
from glob import glob
import xml.etree.ElementTree as ET
from scipy import signal
import numpy as np
import pandas as pd
import ray
from tqdm import tqdm

from wav2sleep.data.edf import load_edf_data
from wav2sleep.data.txt import parse_txt_annotations
from wav2sleep.data.utils import interpolate_index
from wav2sleep.data.xml import parse_xml_annotations
from wav2sleep.parallel import parallelise
from wav2sleep.settings import (
    ABD,
    AIRFLOW,
    CHAT,
    ECG,
    INGEST,
    MROS,
    NEW_AIR,
    SAO2,
    SHHS,
    THX,
    WSC,
)

logger = logging.getLogger(__name__)

# 定义需要处理的信号列，移除了PPG，增加了SaO2和AIRFLOW
EDF_COLS = [SAO2, ECG, ABD, THX, AIRFLOW]

MAX_LENGTH = 60 * 60 * 10  # Recording length in seconds (trimmed to 10h)
# 定义与AHI计算相关的参数
DELTA1 = 5  # 呼吸事件前后的时间窗口（秒）
DELTA2 = 90  # 血氧下降延迟的最大时间（秒）
EPOCH_LENGTH = DELTA1 + DELTA2  # 新的epoch长度（秒）

# 根据新的epoch长度调整索引
TARGET_LABEL_INDEX = pd.Index(np.arange(0, MAX_LENGTH + 1, EPOCH_LENGTH)[1:])
HIGH_FREQ_SIGNAL_INDEX = pd.Index(np.arange(0, MAX_LENGTH + 1e-9, EPOCH_LENGTH / 1024)[1:])  # ~ 34 Hz，10.78 Hz
LOW_FREQ_SIGNAL_INDEX = pd.Index(np.arange(0, MAX_LENGTH + 1e-9, EPOCH_LENGTH / 256)[1:])  # ~ 8 Hz，2.69 Hz

# 定义各信号的固定采样率（Hz）
SAMPLING_RATES = {
    ECG: 125.0,      # ECG信号采样率
    THX: 10.0,       # 胸腔呼吸信号采样率
    ABD: 10.0,       # 腹部呼吸信号采样率
    AIRFLOW: 10.0,   # 气流信号采样率
    NEW_AIR: 10.0,   # 备用气流信号采样率
    SAO2: 1.0        # 血氧饱和度采样率
}

def apply_ecg_filter(signal_data, sample_freq):
    """对ECG信号应用0.3-45Hz带通滤波器"""
    nyquist_freq = 0.5 * sample_freq
    
    # 确保截止频率在0到1之间
    low_cutoff = 0.3 / nyquist_freq
    high_cutoff = 45.0 / nyquist_freq
    
    # 如果采样率太低，导致高截止频率超过1，调整采样率或截止频率
    if high_cutoff >= 1.0:
        print(f"警告：采样频率 {sample_freq}Hz 太低，无法应用45Hz高通滤波。调整为可能的最大值。")
        high_cutoff = 0.95  # 设置为接近1但小于1的值
    
    if low_cutoff >= high_cutoff:
        print(f"警告：低截止频率 ({low_cutoff}) 大于等于高截止频率 ({high_cutoff})。使用默认滤波器。")
        # 使用合理的默认值
        low_cutoff = 0.1
        high_cutoff = 0.8
    
    # 确保截止频率在有效范围内
    low_cutoff = max(0.001, min(low_cutoff, 0.99))
    high_cutoff = max(low_cutoff + 0.001, min(high_cutoff, 0.99))
    
    print(f"应用带通滤波器：采样率={sample_freq}Hz, 低截止={low_cutoff:.4f}, 高截止={high_cutoff:.4f}")
    b, a = signal.butter(4, [low_cutoff, high_cutoff], btype='band')
    return signal.filtfilt(b, a, signal_data)

def apply_respiratory_filters(signal_data, sample_freq):
    """对呼吸信号应用巴特沃斯滤波器:
    1. 0.6Hz低通滤波器，阶数为4
    2. 0.2Hz高通滤波器，阶数为2，消除基线漂移
    """
    nyquist_freq = 0.5 * sample_freq
    
    # 先应用低通滤波
    low_pass_cutoff = 0.6 / nyquist_freq
    # 确保截止频率在有效范围内
    if low_pass_cutoff >= 1.0:
        print(f"警告：采样频率 {sample_freq}Hz 太低，无法应用0.6Hz低通滤波。调整为可能的最大值。")
        low_pass_cutoff = 0.95
    low_pass_cutoff = max(0.001, min(low_pass_cutoff, 0.99))
    
    print(f"应用低通滤波器：采样率={sample_freq}Hz, 截止频率={low_pass_cutoff:.4f}")
    b_low, a_low = signal.butter(4, low_pass_cutoff, btype='low')
    signal_data = signal.filtfilt(b_low, a_low, signal_data)
    
    # 再应用高通滤波
    high_pass_cutoff = 0.2 / nyquist_freq
    # 确保截止频率在有效范围内
    if high_pass_cutoff >= 1.0:
        print(f"警告：采样频率 {sample_freq}Hz 太低，无法应用0.2Hz高通滤波。调整为默认值。")
        high_pass_cutoff = 0.05  # 使用较小的值，因为是高通滤波
    high_pass_cutoff = max(0.001, min(high_pass_cutoff, 0.99))
    
    print(f"应用高通滤波器：采样率={sample_freq}Hz, 截止频率={high_pass_cutoff:.4f}")
    b_high, a_high = signal.butter(2, high_pass_cutoff, btype='high')
    return signal.filtfilt(b_high, a_high, signal_data)

def apply_spo2_filter(signal_data):
    """对血氧饱和度应用Delta过滤器:
    1. 去除不符合生理特征的异常值（例如突降到0）
    2. 保持70-100%的有效区间
    3. 用线性插值填充缺失值
    """
    # 保存原始索引，以便后面填充
    original_index = signal_data.index
    
    # 步骤1和2: 去除异常值，只保留70-100%范围内的值
    mask = (signal_data >= 70) & (signal_data <= 100)
    filtered_signal = signal_data[mask]
    
    # 步骤3: 线性插值填充缺失值
    # 如果过滤后数据为空，返回原始数据
    if len(filtered_signal) == 0:
        return signal_data
    
    # 创建新的Series用于填充，明确指定dtype为float64
    filled_signal = pd.Series(index=original_index, dtype='float64')
    filled_signal[filtered_signal.index] = filtered_signal
    
    # 线性插值填充缺失值
    filled_signal = filled_signal.interpolate(method='linear')
    
    # 处理前后可能无法插值的端点
    filled_signal = filled_signal.fillna(method='ffill').fillna(method='bfill')
    
    # 如果仍有NaN值（整个序列都是NaN的情况），使用中位数填充
    if filled_signal.isna().any():
        # 如果没有任何有效值，使用合理的默认值95填充
        default_value = 95
        filled_signal = filled_signal.fillna(default_value)
    
    return filled_signal

# 添加新的数据填充策略函数
def fill_missing_values(signal_data, method='interpolate_then_zero', valid_range=None):
    """填充缺失值，支持多种策略
    
    Parameters:
    -----------
    signal_data : pd.Series
        包含可能缺失值的信号数据
    method : str
        填充方法：'interpolate_then_zero' - 范围内插值，范围外零填充
                'interpolate_then_nan' - 范围内插值，范围外保持NaN
                'zero' - 全部零填充
                'mean' - 均值填充
    valid_range : tuple, optional
        有效值范围 (min, max)，用于判断是否在生理合理范围内
        
    Returns:
    --------
    pd.Series
        填充后的信号数据
    """
    if signal_data is None or len(signal_data) == 0:
        return signal_data
        
    # 复制以避免修改原始数据
    filled_data = signal_data.copy()
    
    # 如果指定了有效范围，将范围外的值标记为NaN
    if valid_range is not None:
        min_val, max_val = valid_range
        filled_data[(filled_data < min_val) | (filled_data > max_val)] = np.nan
    
    if method == 'interpolate_then_zero':
        # 首先在有效范围内进行插值
        filled_data = filled_data.interpolate(method='linear')
        # 然后使用零填充剩余的NaN值
        filled_data = filled_data.fillna(0)
    
    elif method == 'interpolate_then_nan':
        # 只在有效范围内进行插值，保持范围外的NaN不变
        filled_data = filled_data.interpolate(method='linear')
    
    elif method == 'zero':
        # 直接使用零填充所有NaN
        filled_data = filled_data.fillna(0)
    
    elif method == 'mean':
        # 计算非NaN值的均值，然后填充
        mean_value = filled_data.mean()
        if pd.isna(mean_value):  # 如果全为NaN，则使用0
            mean_value = 0
        filled_data = filled_data.fillna(mean_value)
    
    return filled_data

def process_edf(edf: pd.DataFrame):
    """Process dataframe of EDF data."""
    signals = []
    column_names = []  # 存储列名

    def _process_edf_column(col, target_index, is_ecg=False, is_resp=False, is_spo2=False):
        """Process signal column of EDF with appropriate filtering"""
        if col in edf:
            # 去除NaN值
            clean_data = edf[col].dropna()
            
            # 使用预定义的采样频率，而不是估计
            if col in SAMPLING_RATES:
                sample_freq = SAMPLING_RATES[col]
                print(f"使用{col}的固定采样频率: {sample_freq}Hz")
            else:
                # 如果找不到预定义频率，使用默认值
                sample_freq = 100.0
                print(f"警告: {col}没有预定义采样频率，使用默认值{sample_freq}Hz")
            
            # 应用相应的滤波器
            try:
                if is_ecg:
                    print(f"应用ECG带通滤波器（0.3-45Hz）到 {col} 通道")
                    clean_data_values = clean_data.values.astype(float)
                    filtered_values = apply_ecg_filter(clean_data_values, sample_freq)
                    clean_data = pd.Series(filtered_values, index=clean_data.index)
                elif is_resp:
                    print(f"应用呼吸信号巴特沃斯滤波器到 {col} 通道")
                    clean_data_values = clean_data.values.astype(float)
                    filtered_values = apply_respiratory_filters(clean_data_values, sample_freq)
                    clean_data = pd.Series(filtered_values, index=clean_data.index)
                elif is_spo2:
                    print(f"应用血氧Delta滤波器到 {col} 通道")
                    clean_data = apply_spo2_filter(clean_data)
            except Exception as e:
                print(f"滤波器应用失败，使用原始信号: {e}")
            
            # 重采样到目标索引
            resampled_wav = interpolate_index(clean_data, target_index)
            
            # -- 新的处理流程: 先归一化，后处理无效值 --
            
            # 1. 将生理范围之外的值标记为NaN
            #    这样后续计算均值和标准差时会忽略它们
            signal_to_normalize = resampled_wav.copy()
            if is_spo2:
                # 对于血氧信号，有效范围是70-100
                signal_to_normalize[(signal_to_normalize < 70) | (signal_to_normalize > 100)] = np.nan
            
            # 2. 对有效数据进行归一化 (忽略NaN)
            mean = signal_to_normalize.mean()
            std = signal_to_normalize.std()
            
            # 检查标准差是否为有效数值且大于0
            if pd.notna(std) and std > 0:
                normalized_wav = (signal_to_normalize - mean) / std
            else:
                print(f"警告: {col} 标准差为零或无有效数据，跳过标准化。")
                # 如果有均值，则只进行中心化；否则返回原样（可能全是NaN）
                normalized_wav = signal_to_normalize - mean if pd.notna(mean) else signal_to_normalize

            # 3. 最终结果已包含NaN，符合要求
            signals.append(normalized_wav)
            column_names.append(str(col))  # 确保列名是字符串

    # 应用各种滤波器处理不同信号
    _process_edf_column(ECG, HIGH_FREQ_SIGNAL_INDEX, is_ecg=True)
    _process_edf_column(ABD, LOW_FREQ_SIGNAL_INDEX, is_resp=True)
    _process_edf_column(THX, LOW_FREQ_SIGNAL_INDEX, is_resp=True)
    _process_edf_column(SAO2, LOW_FREQ_SIGNAL_INDEX, is_spo2=True)
    _process_edf_column(AIRFLOW, LOW_FREQ_SIGNAL_INDEX, is_resp=True)
    
    # 创建DataFrame并确保列名是字符串
    result_df = pd.concat(signals, axis=1)
    result_df.columns = column_names  # 使用字符串列名
    
    return result_df.astype(np.float32)


def is_valid_hypopnea(h_event, desats, threshold=2.7):
    """判断低通气事件是否有效，基于血氧饱和度下降是否达到阈值"""
    h_start = float(h_event.get("Start", 0))
    h_end = h_start + float(h_event.get("Duration", 0))

    return any(
        (float(d.get("Start", 0)) >= (h_start - DELTA1) and 
         float(d.get("Start", 0)) <= h_end + DELTA1 + DELTA2) and
        abs(float(d.get("SpO2Baseline", 0)) - float(d.get("SpO2Nadir", 0))) >= threshold
        for d in desats
    )


def process(edf_fp: str, label_fp: str, output_fp: str, overwrite: bool = False) -> bool:
    """Process night of data."""
    if os.path.exists(output_fp) and not overwrite:
        logger.debug(f'Skipping {edf_fp=}, {output_fp=}, already exists')
        return False
    else:
        os.makedirs(os.path.dirname(output_fp), exist_ok=True)

    # 1. 解析XML文件，获取所有事件 - 使用与new_AHI_4H.py完全相同的解析方法
    try:
        # 直接使用ElementTree解析XML，与new_AHI_4H.py保持一致
        tree = ET.parse(label_fp)
        root = tree.getroot()
        events_root = root.find("ScoredEvents")

        all_events = []
        for event in events_root.findall("ScoredEvent"):
            e = {child.tag: child.text for child in event}
            if "Start" in e:
                e["Start"] = float(e["Start"])
            if "Duration" in e:
                e["Duration"] = float(e["Duration"])
            all_events.append(e)
        all_events.sort(key=lambda x: x.get("Start", 0))
    except Exception as e:
        logger.error(f'Failed to parse XML: {label_fp}. Error: {e}')
        return False

    # 提取血氧饱和度下降事件，用于验证低通气事件
    desats = [e for e in all_events if "SpO2 desaturation" in e.get("EventConcept", "")]

    # 2. 初始化标签列，创建一个基于新EPOCH_LENGTH的时间索引
    recording_duration = max(
        [float(event.get("Start", 0)) + float(event.get("Duration", 0)) for event in all_events]
    )
    epochs_index = pd.to_timedelta(np.arange(0, recording_duration, EPOCH_LENGTH), unit='s')
    labels_df = pd.DataFrame(index=epochs_index)
    labels_df['event_label'] = 0  # 默认值0表示无呼吸事件

    # 3. 按照AHI_4H.py的方法识别有效的呼吸事件
    # 定义睡眠阶段映射，与new_AHI_4H.py保持一致
    stage_map = {
        "Wake": 0,
        "Stage 1 sleep": 1,
        "Stage 2 sleep": 2,
        "Stage 3 sleep": 3,
        "REM sleep": 5
    }
    
    # 提取睡眠阶段段
    stage_segments = []
    for e in all_events:
        concept = e.get("EventConcept", "")
        for stage_name, code in stage_map.items():
            if stage_name.lower() in concept.lower():  # 使用小写比较，增强匹配能力
                stage_segments.append({
                    "StageCode": code,
                    "Start": e["Start"],
                    "End": e["Start"] + e["Duration"]
                })
                break
    
    # 统计变量
    total_sleep_duration = 0  # 总睡眠时长（秒）
    total_event_duration = 0  # 总事件时长（秒）
    event_details = []  # 记录每个事件的详细信息
    
    # 有效睡眠阶段代码，与new_AHI_4H.py保持一致
    valid_stages = [1, 2, 3, 5]
    
    # 计算总睡眠时长
    for segment in stage_segments:
        code = segment["StageCode"]
        if code not in valid_stages:
            continue
        start, end = segment["Start"], segment["End"]
        total_sleep_duration += end - start
    
    # 检查睡眠时间是否超过4小时（与new_AHI_4H.py保持一致）
    duration_hr = total_sleep_duration / 3600
    if duration_hr < 4:
        logger.warning(f"睡眠时间仅为 {duration_hr:.2f} 小时，低于4小时阈值，跳过处理")
        return False
    
    # 遍历睡眠阶段，识别有效事件
    valid_event_count = 0
    for segment in stage_segments:
        code = segment["StageCode"]
        if code not in valid_stages:
            continue
        start, end = segment["Start"], segment["End"]
        
        for e in all_events:
            estart = e.get("Start", -1)
            if start <= estart < end:
                concept = e.get("EventConcept", "")
                concept_lower = concept.lower()  # 转换为小写
                valid_event = False
                event_type = "unknown"
                
                # 使用小写进行匹配，确保所有写法都能被识别
                if "obstructive apnea" in concept_lower or "central apnea" in concept_lower or "mixed apnea" in concept_lower:
                    valid_event = True
                    event_type = "apnea"
                    valid_event_count += 1
                elif "hypopnea" in concept_lower:
                    # 使用2.7%的血氧下降阈值验证低通气事件
                    if is_valid_hypopnea(e, desats, threshold=2.7):
                        valid_event = True
                        event_type = "hypopnea"
                        valid_event_count += 1
                
                # 如果是有效事件，将对应时间窗口标记为1
                if valid_event:
                    event_duration = e.get("Duration", 0)
                    total_event_duration += event_duration
                    
                    # 记录事件详情
                    event_details.append({
                        "event_type": event_type,
                        "start_time": estart,
                        "duration": event_duration,
                        "concept": concept
                    })
                    
                    event_start_epoch = pd.to_timedelta(int(estart // EPOCH_LENGTH) * EPOCH_LENGTH, unit='s')
                    event_end = estart + event_duration
                    event_end_epoch = pd.to_timedelta(int(np.ceil(event_end / EPOCH_LENGTH)) * EPOCH_LENGTH, unit='s')
                    
                    # 标记受该事件影响的所有epoch
                    affected_epochs = labels_df.loc[event_start_epoch:event_end_epoch].index
                    labels_df.loc[affected_epochs, 'event_label'] = 1
    
    # 生成事件统计文件
    session_id = os.path.basename(edf_fp).replace('.edf', '')
    stats_output_fp = output_fp.replace('.parquet', '_event_stats.csv')
    
    # 计算AHI，直接使用和new_AHI_4H.py完全相同的计算方式
    ahi = valid_event_count / duration_hr if duration_hr > 0 else 0
    print(f"Session {session_id}: AHI = {ahi:.2f} (Total events: {valid_event_count}, Sleep hours: {duration_hr:.2f})")
    
    # 计算事件比例
    event_ratio = total_event_duration / total_sleep_duration if total_sleep_duration > 0 else 0
    
    # 创建统计信息
    stats_data = {
        "session_id": [session_id],
        "total_sleep_duration_hours": [duration_hr],
        "total_event_duration_seconds": [total_event_duration],
        "event_ratio": [event_ratio],
        "ahi": [ahi],
        "event_count": [valid_event_count],
        "apnea_count": [len([e for e in event_details if e["event_type"] == "apnea"])],
        "hypopnea_count": [len([e for e in event_details if e["event_type"] == "hypopnea"])]
    }
    
    stats_df = pd.DataFrame(stats_data)
    stats_df.to_csv(stats_output_fp, index=False, encoding='utf-8-sig')
    
    # 生成详细事件文件
    if event_details:
        events_output_fp = output_fp.replace('.parquet', '_events_detail.csv')
        events_df = pd.DataFrame(event_details)
        events_df.to_csv(events_output_fp, index=False, encoding='utf-8-sig')
    
    # 将标签的索引与信号对齐
    # 将时间增量索引转换为秒数
    seconds_index = [td.total_seconds() for td in labels_df.index]
    
    # 创建一个Series，索引为秒数，值为event_label
    event_series = pd.Series(labels_df['event_label'].values, index=seconds_index)
    
    # 创建新的DataFrame，使用TARGET_LABEL_INDEX作为索引
    new_labels_df = pd.DataFrame(index=TARGET_LABEL_INDEX, columns=['event_label'])
    
    # 使用索引对齐将值填充到新的DataFrame中
    # 对于TARGET_LABEL_INDEX中存在但seconds_index中不存在的索引，填充0
    new_labels_df['event_label'] = event_series.reindex(index=TARGET_LABEL_INDEX, fill_value=0).values
    
    # 确保类型为整数
    labels_df = new_labels_df.astype(int)

    # 实现更健壮的信号加载策略
    try:
        # 首次尝试加载包含 AIRFLOW 的标准信号列表
        try_edf_cols = EDF_COLS.copy()
        print(f"尝试加载信号通道: {try_edf_cols}")
        edf = load_edf_data(edf_fp, columns=try_edf_cols)
    except (ValueError, KeyError) as e:
        logger.warning(f"加载标准信号通道失败: {e}")
        # 定义备用通道方案
        backup_plans = [
            # 备用方案1: 使用NEW_AIR替代AIRFLOW
            {AIRFLOW: NEW_AIR},
            # 备用方案2: 尝试不同的ECG通道名称
            {ECG: 'ECG1'}, 
            {ECG: 'EKG'},
            # 备用方案3: 尝试不同的呼吸信号通道名称
            {ABD: 'ABDO'}, 
            {THX: 'THOR'},
            {AIRFLOW: 'FLOW'},
            # 备用方案4: 尝试不同的血氧通道名称
            {SAO2: 'SpO2'},
            {SAO2: 'SaO2'},
        ]
        
        # 尝试各种备用方案
        loaded = False
        for plan in backup_plans:
            try:
                # 创建一个新的通道列表，替换原始通道
                alt_cols = try_edf_cols.copy()
                for orig_col, alt_col in plan.items():
                    if orig_col in alt_cols:
                        idx = alt_cols.index(orig_col)
                        alt_cols[idx] = alt_col
                
                print(f"尝试备用通道方案: {alt_cols}")
                edf = load_edf_data(edf_fp, columns=alt_cols)
                
                # 如果成功加载，将通道名称映射回原始名称
                for orig_col, alt_col in plan.items():
                    if alt_col in edf.columns:
                        edf = edf.rename(columns={alt_col: orig_col})
                
                loaded = True
                print(f"成功使用备用通道加载: {list(edf.columns)}")
                break
            except Exception as e2:
                print(f"备用通道加载失败: {e2}")
                continue
        
        if not loaded:
            # 如果所有方案都失败，尝试加载所有可用通道并选择必要的部分
            try:
                print("尝试加载所有可用通道...")
                edf_all = load_edf_data(edf_fp)
                print(f"可用通道: {list(edf_all.columns)}")
                
                # 尝试寻找合适的替代通道
                available_cols = list(edf_all.columns)
                found_cols = {}
                
                # 搜索每个所需通道的可能替代
                for required_col in EDF_COLS:
                    # 尝试精确匹配
                    if required_col in available_cols:
                        found_cols[required_col] = required_col
                    else:
                        # 尝试模糊匹配
                        for col in available_cols:
                            if required_col.lower() in col.lower() or col.lower() in required_col.lower():
                                found_cols[required_col] = col
                                break
                
                # 如果至少找到了一个通道，创建过滤后的DataFrame
                if found_cols:
                    print(f"找到以下通道映射: {found_cols}")
                    edf = edf_all[[found_cols.get(col, col) for col in found_cols.keys()]]
                    
                    # 重命名列
                    edf = edf.rename(columns={v: k for k, v in found_cols.items()})
                else:
                    raise ValueError("无法找到任何所需通道的替代")
                    
            except Exception as e3:
                logger.error(f"所有加载方案都失败，无法处理文件 {edf_fp}: {e3}")
                return False
    
    # 捕获任何其他异常
    except Exception as e:
        logger.error(f"加载EDF时发生未预期的错误: {e}")
        return False

    try:
        # 处理信号
        waveform_df = process_edf(edf)
        
        # 确保事件标签列名是字符串
        labels_df['event_label'] = labels_df['event_label'].astype(int)
        
        # 合并数据并确保所有列名都是字符串
        output_df = pd.concat([waveform_df, labels_df['event_label']], axis=1)
        
        # 打印列名类型，以便确认所有列名都是字符串
        print(f"DataFrame列名类型: {[type(col) for col in output_df.columns]}")
        
        # 如果仍有非字符串列名，强制转换所有列名为字符串
        if not all(isinstance(col, str) for col in output_df.columns):
            print("警告: 发现非字符串列名，进行强制转换")
            output_df.columns = [str(col) for col in output_df.columns]
        
        # 检查是否有空值，这是预期行为
        nan_cols = output_df.columns[output_df.isna().any()].tolist()
        if nan_cols:
            print(f"信息: 以下列包含预期的NaN值: {nan_cols}")
            # 根据新策略，信号列中的NaN是允许的，代表无效或缺失数据。
            # 我们只处理不应有NaN的列，例如 'event_label'
            if 'event_label' in nan_cols:
                output_df['event_label'] = output_df['event_label'].fillna(0).astype(int)
                print("  -> 已将 'event_label' 列中的NaN值填充为0。")

        # 检查并报告最终的NaN状态
        if output_df.isna().any().any():
            print("信息: 输出数据按预期包含NaN值。")
        else:
            print("信息: 输出数据不包含NaN值。")
        
        # 保存到parquet文件
        output_df.to_parquet(output_fp)
        print(f"成功处理并保存到: {output_fp}")
        return True
        
    except Exception as e:
        logger.error(f"处理信号时发生错误: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False


def get_edf_path(session_id: str, dataset: str, folder: str):
    if dataset == SHHS:
        partition, _ = session_id.split('-')  # shhs1 or shhs2
        edf_fp = os.path.join(folder, 'polysomnography/edfs', partition, f'{session_id}.edf')
    elif dataset == MROS:
        _, partition, *_ = session_id.split('-')  # mros visit 1 or 2
        edf_fp = os.path.join(folder, 'polysomnography/edfs', partition, f'{session_id}.edf')
    elif dataset == CHAT:
        if 'nonrandomized' in session_id:  # e.g. chat-baseline-nonrandomized-xxxx
            partition = 'nonrandomized'
        else:
            partition = session_id.split('-')[1]  # e.g. chat-baseline-xxxx
        edf_fp = os.path.join(folder, 'polysomnography/edfs', partition, f'{session_id}.edf')
        fixed_edf_fp = edf_fp.replace('.edf', '_fixed.edf')
        # Check for existence of fixed EDF (physical maximum is 0.0 in some files and needed fixing.)
        if os.path.exists(fixed_edf_fp):
            edf_fp = fixed_edf_fp
    else:
        edf_fp = os.path.join(folder, 'polysomnography/edfs', f'{session_id}.edf')
    return edf_fp


def prepare_dataset(folder: str, output_folder: str, dataset: str):
    """Prepare dataset IO locations for parallel processing."""
    # WSC uses .txt annotation files
    fp_dict = {}
    if dataset == WSC:
        edf_fps = glob(f'{folder}/polysomnography/*.edf', recursive=True)
        label_fps = []
        for edf_fp in edf_fps:
            all_score_fp = edf_fp.replace('.edf', '.allscore.txt')
            stg_fp = edf_fp.replace('.edf', '.stg.txt')
            if os.path.exists(stg_fp):
                label_fp = stg_fp
            elif os.path.exists(all_score_fp):
                label_fp = all_score_fp
            else:
                continue
            session_id = os.path.basename(edf_fp).replace('.edf', '')
            output_fp = os.path.join(output_folder, dataset, INGEST, f'{session_id}.parquet')
            fp_dict[session_id] = {'edf_fp': edf_fp, 'label_fp': label_fp, 'output_fp': output_fp}
        return fp_dict
    # Other datasets use NSRR standardized XML files
    label_fps = glob(f'{folder}/polysomnography/annotations-events-nsrr/**/**.xml', recursive=True)
    for label_fp in label_fps:
        session_id = os.path.basename(label_fp).replace('-nsrr.xml', '')
        edf_fp = get_edf_path(session_id, dataset, folder)
        if not os.path.exists(edf_fp):
            logger.warning(f"{edf_fp=} doesn't exist. Skipping...")
            continue

        # 根据session_id判断是shhs1还是shhs2，用于创建独立的输出文件夹
        if dataset == SHHS:
            output_subfolder = session_id.split('-')[0]
        else:
            output_subfolder = dataset

        output_fp = os.path.join(output_folder, output_subfolder, INGEST, f'{session_id}.parquet')
        fp_dict[session_id] = {'edf_fp': edf_fp, 'label_fp': label_fp, 'output_fp': output_fp}
    return fp_dict


def process_files(
    fp_dict: dict[str, dict[str, str]], max_parallel: int = 1, overwrite: bool = False, address: str = 'local'
):
    print(f'Preparing to process {len(fp_dict)} files.')

    def proc(arg_dict):
        try:
            return process(overwrite=overwrite, **arg_dict)
        except Exception as e:
            logger.error(f'Failed on {arg_dict} - {e}')
            print(f'Failed on {arg_dict} - {e}')
            return False

    if max_parallel > 1:
        # MEM_GB = int(os.environ.get('SLURM_MEM_PER_NODE', 8 * 1024)) / 1024  # SLURM env var. is in MB. Removing this as it can cause issues on Windows.
        ray.init(
            address=address,
            num_cpus=max_parallel,
            # object_store_memory=MEM_GB * 1024**3, # Let Ray manage memory automatically.
            ignore_reinit_error=True,
            include_dashboard=False,
        )
        num_converted = sum(parallelise(proc, fp_dict.values(), use_tqdm=True, max_parallel=max_parallel))
    else:
        num_converted = 0
        for fp_map in tqdm(fp_dict.values()):
            num_converted += process(**fp_map)
    print(f'Converted {num_converted} files.')


def parse_args():
    parser = argparse.ArgumentParser(prog='Dataset Processor', description='Process dataset.')
    parser.add_argument('--folder', help='Location of dataset.')
    parser.add_argument('--max-parallel', default=1, type=int, help='Parallel processes.')
    parser.add_argument('--cluster-address', default='local', type=str, help='Ray cluster address (defaults to local).')
    parser.add_argument('--output-folder', required=True, help='Base output folder for processed datasets.')
    parser.add_argument(
        '--overwrite',
        action='store_true',
        help='Overwrite existing parquet files.',
        default=False,
    )
    return parser.parse_args()


def main() -> None:
    # ====== 你可以在这里手动设置参数 ======
    class Args:
        folder = None  # 将自动从命令行参数获取，或使用默认值
        output_folder = None  # 将自动从命令行参数获取，或使用默认值
        # 自动检测CPU核心数并设置并行度
        max_parallel = 1  # 强制设置为1，以禁用并行处理
        cluster_address = "local"
        overwrite = False
    
    # 尝试解析命令行参数，如果失败则使用默认值
    try:
        cmd_args = parse_args()
        args = Args()
        args.folder = cmd_args.folder
        args.output_folder = cmd_args.output_folder
        args.max_parallel = cmd_args.max_parallel
        args.cluster_address = cmd_args.cluster_address
        args.overwrite = cmd_args.overwrite
    except:
        # 命令行参数解析失败，使用默认值
        args = Args()
        
    # 设置默认值
    if not args.folder:
        # 默认数据集路径
        args.folder = r"E:\Desktop\OSA\shhs"
        print(f"使用默认数据集路径: {args.folder}")
    
    if not args.output_folder:
        # 构建默认输出路径
        args.output_folder = os.path.join(os.path.dirname(args.folder), os.path.basename(args.folder) + "_processed")
        print(f"使用默认输出路径: {args.output_folder}")
        
    # 由于Ray在Windows上可能不稳定，我们默认禁用并行，除非用户明确指定
    if args.max_parallel > 1:
        print(f"用户指定并行处理数: {args.max_parallel}")
    else:
        args.max_parallel = 1
        print("已禁用并行处理，将以单线程模式运行。")
    # =====================================

    # 确保输出目录存在
    os.makedirs(args.output_folder, exist_ok=True)
    
    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(os.path.join(args.output_folder, "processing_log.txt")),
            logging.StreamHandler()
        ]
    )
    
    # 记录处理开始信息
    logger.info(f"开始处理数据集: {args.folder}")
    logger.info(f"输出目录: {args.output_folder}")
    logger.info(f"并行度: {args.max_parallel}")
    logger.info(f"覆盖现有文件: {args.overwrite}")

    # 推断数据集名称
    dataset = os.path.basename(args.folder)  # 例如 path/to/mros
    print(f'Processing {dataset=}...')
    
    # 准备处理
    fp_dict = prepare_dataset(folder=args.folder, output_folder=args.output_folder, dataset=dataset)
    logger.info(f"找到 {len(fp_dict)} 个文件等待处理")
    
    # 处理文件
    process_files(fp_dict, max_parallel=args.max_parallel, overwrite=args.overwrite, address=args.cluster_address)
    
    # 记录处理完成信息
    logger.info(f"数据集处理完成: {args.folder}")

if __name__ == '__main__':
    main()
