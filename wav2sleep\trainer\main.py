"""PyTorch lightning model classes for sleep transformer models."""
__all__ = ('SleepLightningModel',)
import logging
import io
import warnings
import hydra
import matplotlib
matplotlib.use('Agg')  # 必须放在其他matplotlib导入之前
from matplotlib import pyplot as plt
from collections import defaultdict
from typing import Callable, Iterator, Optional, Dict, Any
import torch.nn as nn
import lightning
import torch
import torch.distributed as dist
import numpy as np
from lightning.pytorch.loggers import M<PERSON>lowLogger
from lightning.pytorch.trainer.states import RunningStage
from torch.nn import Parameter
from torch.optim import Optimizer
from torch.optim.lr_scheduler import ReduceLROnPlateau, _LRScheduler
from torchmetrics.classification import MulticlassConfusionMatrix
from torchmetrics.regression import MeanAbsoluteError, MeanSquaredError, R2Score, PearsonCorrCoef
import torch.nn.functional as F
import warnings
import os
from PIL import Image  # 新增PIL导入
from wav2sleep.log import log_aux_metrics
from wav2sleep.models.wav2sleep import Wav2Sleep
from ..settings import CCSHS, CFS, CHAT, ECG, MESA, PPG, SHHS, TEST, THX, TRAIN, VAL, denormalize_ahi, ahi_to_class
from .masker import SignalMasker
from ..settings import AHI_MAX_VALUE
logger = logging.getLogger(__name__)

CLASS_NAMES = ['Normal', 'Mild', 'Moderate', 'Severe']

def sum_if_distributed(tensor):
    """Sum tensor across all GPUs."""
    if dist.is_initialized():
        dist.barrier()
        dist.all_reduce(tensor, op=dist.ReduceOp.SUM)
    return tensor

def ahi_to_class_tensor(ahi: torch.Tensor) -> torch.Tensor:
    """Convert AHI tensor values to classification labels (0-3)"""
    classes = torch.zeros_like(ahi, dtype=torch.long)
    classes[(ahi >= 5) & (ahi < 15)] = 1
    classes[(ahi >= 15) & (ahi < 30)] = 2
    classes[ahi >= 30] = 3
    return classes

def compute_mape(y_pred: torch.Tensor, y_true: torch.Tensor, epsilon: float = 1e-8) -> torch.Tensor:
    """计算平均绝对百分比误差 (MAPE)"""
    # 避免除零，对于接近0的真实值使用epsilon
    denominator = torch.clamp(torch.abs(y_true), min=epsilon)
    return torch.mean(torch.abs((y_true - y_pred) / denominator)) * 100

def confusion_matrix(y_pred: torch.Tensor, y_true: torch.Tensor, 
                    cmat_func: MulticlassConfusionMatrix) -> torch.Tensor:
    """处理分类标签的混淆矩阵"""
    valid_mask = y_true >= 0
    y_true_valid = y_true[valid_mask].to(cmat_func.device)  # 确保输入数据与指标同设备
    y_pred_valid = y_pred[valid_mask].to(cmat_func.device)
    
    pred_class = ahi_to_class(y_pred_valid)
    true_class = ahi_to_class(y_true_valid)
    
    return cmat_func(pred_class-1, true_class-1)

class SleepLightningModel(lightning.LightningModule):
    """AHI回归预测模型"""
    
    def __init__(
        self,
        model: Wav2Sleep,
        criterion: Dict[str, Any],
        optimizer: Callable[[Iterator[Parameter]], Optimizer],
        aux_metrics=None,
        scheduler: Optional[Callable[[Optimizer], _LRScheduler]] = None,
        debug_level=2,
        on_step: bool = False,
        on_epoch: bool = True,
        masker: SignalMasker | None = None,
        flip_polarity: bool = True,
        **kwargs
    ):
        super().__init__()
        _ = kwargs.pop('num_classes', None)
        
        self.model = model
        self.optimizer = optimizer
        self.scheduler = scheduler
        self.aux_metrics = aux_metrics
        
        # 动态创建损失函数（完全覆盖原配置）
        self.criterion = self._create_safe_criterion(criterion)
        
        # 二分类指标初始化
        from torchmetrics.classification import BinaryAccuracy, BinaryConfusionMatrix
        
        self.acc_metric = BinaryAccuracy().to(self.device)
        self.cmat_func = BinaryConfusionMatrix().to(self.device)
        
        # 回归指标初始化
        self.mae_metric = MeanAbsoluteError()
        self.mse_metric = MeanSquaredError()
        self.r2_metric = R2Score()
        self.pearson_metric = PearsonCorrCoef()
        self.cmat_func = MulticlassConfusionMatrix(num_classes=4)

        # 确保所有指标都在正确的设备上
        self.mae_metric = self.mae_metric.to(self.device)
        self.mse_metric = self.mse_metric.to(self.device)
        self.r2_metric = self.r2_metric.to(self.device)
        self.pearson_metric = self.pearson_metric.to(self.device)
        self.cmat_func = self.cmat_func.to(self.device)
        self.aux_outputs = {
            mode: defaultdict(lambda: defaultdict(lambda: 0.0))
            for mode in (TRAIN, VAL, TEST)
        }
        
        # 运行时参数
        self.debug_level = debug_level
        self.on_epoch = on_epoch
        self.on_step = on_step
        self.masker = masker
        self.flip_polarity = flip_polarity
        self.unified = len(model.signal_encoders) > 1
        
        # 预测存储（关键修复2：初始化所有存储列表）
        self.train_preds = []
        self.train_targets = []
        self.val_preds = []
        self.val_targets = []
        self.test_preds = []
        self.test_targets = []

    def _create_safe_criterion(self, cfg: Dict[str, Any]) -> nn.Module:
        """创建AHI回归损失函数"""
        try:
            # 使用MSE Loss，更适合回归任务
            return nn.MSELoss()
        except Exception as e:
            warnings.warn(f"损失函数创建失败，使用默认MSELoss: {str(e)}")
            return nn.MSELoss()

    def forward(self, x: dict[str, torch.Tensor]) -> torch.Tensor:
        return self.model(x)

    def get_ds_name(self, dataloader_idx: int, mode: str):
        """获取数据集名称"""
        if mode == TRAIN:
            return 'all'
        elif mode == VAL:
            return self.trainer.datamodule.val_dataset_map[dataloader_idx]
        return self.trainer.datamodule.test_dataset_map[dataloader_idx]

    def _step(self, batch, mode: str, dataloader_idx: int = 0):
        x, y_true = batch
        y_pred = self(x)  # 输出已经是概率值[0,1]
        ds_name = self.get_ds_name(dataloader_idx, mode)
        y_true = y_true.float().flatten()  # 确保标签是浮点数
        
        # 数据验证
        if y_true.dim() != 1:
            raise RuntimeError(f"目标形状应为[B], 实际得到{y_true.shape}")
        if y_pred.shape != y_true.shape:
            raise RuntimeError(f"预测和目标的形状不匹配: {y_pred.shape} vs {y_true.shape}")
        
        # 计算回归损失
        loss = self.criterion(y_pred, y_true)

        # 反归一化用于指标计算，保持在GPU上
        y_pred_denorm = denormalize_ahi(y_pred)
        y_true_denorm = denormalize_ahi(y_true)

        # 计算回归指标
        with torch.no_grad():
            mae = self.mae_metric(y_pred_denorm, y_true_denorm)
            mse = self.mse_metric(y_pred_denorm, y_true_denorm)
            rmse = torch.sqrt(mse)

            # R²和Pearson需要至少2个样本
            if len(y_pred_denorm) >= 2:
                r2 = self.r2_metric(y_pred_denorm, y_true_denorm)
                pearson = self.pearson_metric(y_pred_denorm, y_true_denorm)
            else:
                r2 = torch.tensor(0.0, device=self.device)
                pearson = torch.tensor(0.0, device=self.device)

            mape = compute_mape(y_pred_denorm, y_true_denorm)

            # 分类准确率（基于AHI阈值）
            pred_classes = ahi_to_class_tensor(y_pred_denorm)
            true_classes = ahi_to_class_tensor(y_true_denorm)
            class_acc = (pred_classes == true_classes).float().mean()

        # 记录指标
        self.log_dict({
            f'{mode}_loss': loss,
            f'{mode}_mae': mae,
            f'{mode}_rmse': rmse,
            f'{mode}_r2': r2,
            f'{mode}_pearson': pearson,
            f'{mode}_mape': mape,
            f'{mode}_class_acc': class_acc
        }, prog_bar=True, sync_dist=True)
        
        # 存储预测（保持设备一致性）
        if mode == TRAIN:
            self.train_preds.append(y_pred.detach())
            self.train_targets.append(y_true.detach())
        elif mode == VAL:
            self.val_preds.append(y_pred.detach())
            self.val_targets.append(y_true.detach())
        elif mode == TEST:
            self.test_preds.append(y_pred.detach())
            self.test_targets.append(y_true.detach())
            
        return loss
    def training_step(self, batch, batch_idx):
        signals, _ = batch
        if self.flip_polarity:
            invert_signals(signals)
        if self.unified and self.masker is not None:
            self.masker(signals)
        return self._step(batch, mode=TRAIN)

    def validation_step(self, batch, batch_idx, dataloader_idx=0):
        return self._step(batch, mode=VAL, dataloader_idx=dataloader_idx)

    def test_step(self, batch, batch_idx, dataloader_idx=0):  
        return self._step(batch, mode=TEST, dataloader_idx=dataloader_idx)
    
    def on_validation_epoch_end(self):
        if not self.val_preds:
            return
            
        # 合并数据并同步设备
        val_preds = torch.cat(self.val_preds).to(self.device)
        val_targets = torch.cat(self.val_targets).to(self.device)
        
        # 生成图片（仅在主进程）
        if self.trainer.is_global_zero:
            fig = self._create_visualizations(val_preds.cpu().numpy(), val_targets.cpu().numpy())
            os.makedirs("mulruns/plots", exist_ok=True)
            img_path = f"mulruns/plots/val_epoch{self.current_epoch}.png"
            fig.savefig(img_path, bbox_inches='tight', dpi=300)
            plt.close(fig)
            
            # 记录到MLFlow
            if isinstance(self.logger, MLFlowLogger):
                try:
                    self.logger.experiment.log_artifact(
                        self.logger.run_id,
                        img_path,
                        artifact_path="ahi_plots"
                    )
                except Exception as e:
                    logger.error(f"记录验证图片失败: {str(e)}")
        
        self.val_preds.clear()
        self.val_targets.clear()
    
    def on_test_epoch_end(self):
        if not self.test_preds:
            return
            
        # 分布式数据同步
        if torch.distributed.is_initialized():
            preds = torch.cat(self.test_preds).to(self.device)
            targets = torch.cat(self.test_targets).to(self.device)
            
            pred_list = [torch.zeros_like(preds) for _ in range(dist.get_world_size())]
            dist.all_gather(pred_list, preds)
            preds = torch.cat(pred_list)
            
            target_list = [torch.zeros_like(targets) for _ in range(dist.get_world_size())]
            dist.all_gather(target_list, targets)
            targets = torch.cat(target_list)
        else:
            preds = torch.cat(self.test_preds)
            targets = torch.cat(self.test_targets)
        
        # 生成图片（仅在主进程）
        if self.trainer.is_global_zero:
            fig = self._create_visualizations(preds.cpu().numpy(), targets.cpu().numpy())
            os.makedirs("mulruns/plots", exist_ok=True)
            img_path = f"mulruns/plots/test_epoch{self.current_epoch}.png"
            fig.savefig(img_path, bbox_inches='tight', dpi=300)
            plt.close(fig)
            
            # 记录到MLFlow
            if isinstance(self.logger, MLFlowLogger):
                try:
                    self.logger.experiment.log_artifact(
                        self.logger.run_id,
                        img_path,
                        artifact_path="ahi_plots"
                    )
                except Exception as e:
                    logger.error(f"记录测试图片失败: {str(e)}")
        
        self.test_preds.clear()
        self.test_targets.clear()

    def on_train_epoch_end(self):
        if not self.train_preds:
            return
            
        # 合并数据
        train_preds = torch.cat(self.train_preds).to(self.device)
        train_targets = torch.cat(self.train_targets).to(self.device)
        
        # 生成图片（仅在主进程）
        if self.trainer.is_global_zero:
            fig = self._create_visualizations(train_preds.cpu().numpy(), train_targets.cpu().numpy())
            os.makedirs("mulruns/plots", exist_ok=True)
            img_path = f"mulruns/plots/train_epoch{self.current_epoch}.png"
            fig.savefig(img_path, bbox_inches='tight', dpi=300)
            plt.close(fig)
            
            # 记录到MLFlow
            if isinstance(self.logger, MLFlowLogger):
                try:
                    self.logger.experiment.log_artifact(
                        self.logger.run_id,
                        img_path,
                        artifact_path="ahi_plots"
                    )
                except Exception as e:
                    logger.error(f"记录训练图片失败: {str(e)}")
        
        self.train_preds.clear()
        self.train_targets.clear()
    
    def _create_visualizations(self, preds, targets):
        """创建AHI回归可视化图表"""
        # 反归一化预测和目标值
        preds_denorm = np.array([denormalize_ahi(p) for p in preds])
        targets_denorm = np.array([denormalize_ahi(t) for t in targets])

        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))

        # 回归散点图
        ax1.scatter(targets_denorm, preds_denorm, alpha=0.3, s=20)
        ax1.plot([0, 80], [0, 80], 'r--', lw=2)  # 理想预测线

        # 计算指标
        mae = np.mean(np.abs(preds_denorm - targets_denorm))
        rmse = np.sqrt(np.mean((preds_denorm - targets_denorm)**2))
        r2 = np.corrcoef(preds_denorm, targets_denorm)[0, 1]**2

        ax1.set_xlabel('True AHI')
        ax1.set_ylabel('Predicted AHI')
        ax1.set_title(f'AHI Regression\nMAE: {mae:.2f}, RMSE: {rmse:.2f}, R²: {r2:.3f}')
        ax1.set_xlim([0, 80])
        ax1.set_ylim([0, 80])
        ax1.grid(True, alpha=0.3)

        # 分类混淆矩阵
        from sklearn.metrics import confusion_matrix
        pred_classes = np.array([ahi_to_class(p) for p in preds_denorm])
        true_classes = np.array([ahi_to_class(t) for t in targets_denorm])
        cm = confusion_matrix(true_classes, pred_classes, labels=[0, 1, 2, 3])
        
        im = ax2.imshow(cm, cmap='Blues')
        ax2.set_xticks([0, 1, 2, 3])
        ax2.set_yticks([0, 1, 2, 3])
        ax2.set_xticklabels(['Normal', 'Mild', 'Moderate', 'Severe'])
        ax2.set_yticklabels(['Normal', 'Mild', 'Moderate', 'Severe'])
        ax2.set_xlabel('Predicted Class')
        ax2.set_ylabel('True Class')

        # 计算分类准确率
        class_acc = np.sum(np.diag(cm)) / np.sum(cm) if np.sum(cm) > 0 else 0
        ax2.set_title(f'AHI Classification\n(Accuracy: {class_acc:.1%})')

        # 添加数值标注
        for i in range(4):
            for j in range(4):
                if i < cm.shape[0] and j < cm.shape[1]:
                    count = cm[i, j]
                    ax2.text(
                        j, i, str(count),
                        ha='center', va='center',
                        color='white' if count > cm.max()/2 else 'black'
                    )
        
        plt.tight_layout()
        return fig

    def configure_optimizers(self):
        optimizer = self.optimizer(self.model.parameters())
        if self.scheduler is None:
            return optimizer
        
        scheduler = self.scheduler(optimizer)
        return {
            'optimizer': optimizer,
            'lr_scheduler': {
                'scheduler': scheduler,
                'monitor': 'val_loss',
                'interval': 'epoch',
                'frequency': 1
            }
        }

def invert_signals(signals: dict[str, torch.Tensor]):
    """随机翻转信号极性"""
    for name, x in signals.items():
        flip = torch.randint(0, 2, (x.size(0),1), device=x.device)*2-1
        signals[name] = x * flip