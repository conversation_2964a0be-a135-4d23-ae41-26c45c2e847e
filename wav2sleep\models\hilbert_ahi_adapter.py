"""
希尔伯特降维适配器 - 专门为多通道生理信号AHI回归任务设计
基于随机傅里叶特征的高效降维方法
"""

import torch
import torch.nn as nn
import math
import numpy as np
from typing import Dict, Tuple
import time

from ..Hierbot import ResidualEnhancedHilbertMapping


class PhysiologicalSignalHilbertMapper(nn.Module):
    """生理信号希尔伯特映射器
    
    专门为5个生理信号通道设计的降维器：
    - ECG: 387,789 -> 128维特征
    - SAO2/ABD/THX/AIRFLOW: 96,947 -> 64维特征
    总计: 484,735 -> 384维特征 (约1260倍压缩)
    """
    
    def __init__(self, 
                 ecg_output_dim=128,
                 other_output_dim=64,
                 gamma=0.01,
                 use_residual=True):
        super().__init__()
        
        self.ecg_output_dim = ecg_output_dim
        self.other_output_dim = other_output_dim
        self.use_residual = use_residual
        
        # ECG信号映射器 (高频信号，需要更多特征)
        self.ecg_mapper = ResidualEnhancedHilbertMapping(
            input_dim=387_789,
            output_dim=ecg_output_dim,
            gamma=gamma * 0.1,  # ECG使用较小的gamma，保留更多细节
            residual_ratio=0.15 if use_residual else 0.0
        )
        
        # 其他信号映射器 (低频信号)
        self.sao2_mapper = ResidualEnhancedHilbertMapping(
            input_dim=96_947,
            output_dim=other_output_dim,
            gamma=gamma * 0.5,  # 血氧信号使用中等gamma
            residual_ratio=0.1 if use_residual else 0.0
        )
        
        self.abd_mapper = ResidualEnhancedHilbertMapping(
            input_dim=96_947,
            output_dim=other_output_dim,
            gamma=gamma,  # 呼吸信号使用标准gamma
            residual_ratio=0.1 if use_residual else 0.0
        )
        
        self.thx_mapper = ResidualEnhancedHilbertMapping(
            input_dim=96_947,
            output_dim=other_output_dim,
            gamma=gamma,  # 呼吸信号使用标准gamma
            residual_ratio=0.1 if use_residual else 0.0
        )
        
        self.airflow_mapper = ResidualEnhancedHilbertMapping(
            input_dim=96_947,
            output_dim=other_output_dim,
            gamma=gamma * 2.0,  # 气流信号使用较大gamma，更关注全局模式
            residual_ratio=0.1 if use_residual else 0.0
        )
        
        # 总输出维度
        self.total_output_dim = ecg_output_dim + 4 * other_output_dim
        
        # 性能监控
        self.register_buffer('forward_count', torch.tensor(0))
        self.register_buffer('total_time', torch.tensor(0.0))
        
    def forward(self, x_dict: Dict[str, torch.Tensor]) -> torch.Tensor:
        """
        前向传播
        
        Args:
            x_dict: 包含5个信号的字典
                - 'ECG': (batch_size, 387789)
                - 'SAO2': (batch_size, 96947) 
                - 'ABD': (batch_size, 96947)
                - 'THX': (batch_size, 96947)
                - 'AIRFLOW': (batch_size, 96947)
        
        Returns:
            降维后的特征: (batch_size, total_output_dim)
        """
        start_time = time.time()
        
        batch_size = next(iter(x_dict.values())).size(0)
        device = next(iter(x_dict.values())).device
        
        # 存储各信号的映射结果
        mapped_features = []
        
        # 处理ECG信号
        if 'ECG' in x_dict:
            ecg_signal = x_dict['ECG']
            # 处理NaN值
            ecg_signal = torch.nan_to_num(ecg_signal, nan=0.0)
            ecg_features = self.ecg_mapper(ecg_signal)
            mapped_features.append(ecg_features)
        else:
            # 如果ECG不存在，用零填充
            mapped_features.append(torch.zeros(batch_size, self.ecg_output_dim, device=device))
        
        # 处理其他信号
        signal_mappers = {
            'SAO2': self.sao2_mapper,
            'ABD': self.abd_mapper,
            'THX': self.thx_mapper,
            'AIRFLOW': self.airflow_mapper
        }
        
        for signal_name, mapper in signal_mappers.items():
            if signal_name in x_dict:
                signal = x_dict[signal_name]
                # 处理NaN值
                signal = torch.nan_to_num(signal, nan=0.0)
                # 确保信号长度正确
                if signal.size(1) != 96_947:
                    # 如果长度不对，进行自适应池化
                    signal = torch.nn.functional.adaptive_avg_pool1d(
                        signal.unsqueeze(1), 96_947
                    ).squeeze(1)
                features = mapper(signal)
                mapped_features.append(features)
            else:
                # 如果信号不存在，用零填充
                mapped_features.append(torch.zeros(batch_size, self.other_output_dim, device=device))
        
        # 拼接所有特征
        final_features = torch.cat(mapped_features, dim=1)
        
        # 更新性能统计
        self.forward_count += 1
        self.total_time += time.time() - start_time
        
        return final_features
    
    def get_compression_stats(self):
        """获取压缩统计信息"""
        original_dims = {
            'ECG': 387_789,
            'SAO2': 96_947,
            'ABD': 96_947,
            'THX': 96_947,
            'AIRFLOW': 96_947
        }
        
        compressed_dims = {
            'ECG': self.ecg_output_dim,
            'SAO2': self.other_output_dim,
            'ABD': self.other_output_dim,
            'THX': self.other_output_dim,
            'AIRFLOW': self.other_output_dim
        }
        
        total_original = sum(original_dims.values())
        total_compressed = sum(compressed_dims.values())
        compression_ratio = total_original / total_compressed
        
        return {
            'original_dims': original_dims,
            'compressed_dims': compressed_dims,
            'total_original': total_original,
            'total_compressed': total_compressed,
            'compression_ratio': compression_ratio,
            'memory_reduction': f"{(1 - total_compressed/total_original)*100:.1f}%"
        }
    
    def get_performance_stats(self):
        """获取性能统计信息"""
        forward_count = max(self.forward_count.item(), 1)
        avg_time = (self.total_time / forward_count).item() * 1000  # ms
        
        # 获取各个映射器的统计
        mappers_stats = {
            'ecg': self.ecg_mapper.get_performance_stats(),
            'sao2': self.sao2_mapper.get_performance_stats(),
            'abd': self.abd_mapper.get_performance_stats(),
            'thx': self.thx_mapper.get_performance_stats(),
            'airflow': self.airflow_mapper.get_performance_stats()
        }
        
        # 计算总FLOPs
        total_flops = sum(stats['flops_total'] for stats in mappers_stats.values())
        
        return {
            'forward_count': forward_count,
            'avg_forward_time_ms': avg_time,
            'total_flops': total_flops,
            'flops_per_sample': total_flops,
            'mappers_stats': mappers_stats
        }
    
    def optimize_for_inference(self):
        """优化所有映射器用于推理"""
        print("优化希尔伯特映射器用于推理...")
        
        self.ecg_mapper.optimize_for_inference()
        self.sao2_mapper.optimize_for_inference()
        self.abd_mapper.optimize_for_inference()
        self.thx_mapper.optimize_for_inference()
        self.airflow_mapper.optimize_for_inference()
        
        # 重置性能计数器
        self.forward_count.zero_()
        self.total_time.zero_()
        
        compression_stats = self.get_compression_stats()
        print(f"压缩比: {compression_stats['compression_ratio']:.1f}x")
        print(f"内存减少: {compression_stats['memory_reduction']}")


class HilbertAHIPredictor(nn.Module):
    """基于希尔伯特降维的AHI预测器"""
    
    def __init__(self,
                 ecg_output_dim=128,
                 other_output_dim=64,
                 hidden_dim=256,
                 dropout=0.3,
                 gamma=0.01):
        super().__init__()
        
        # 希尔伯特降维器
        self.hilbert_mapper = PhysiologicalSignalHilbertMapper(
            ecg_output_dim=ecg_output_dim,
            other_output_dim=other_output_dim,
            gamma=gamma,
            use_residual=True
        )
        
        # 获取总特征维度
        total_features = self.hilbert_mapper.total_output_dim
        
        # AHI回归网络
        self.ahi_regressor = nn.Sequential(
            nn.Linear(total_features, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.LayerNorm(hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            
            nn.Linear(hidden_dim // 2, hidden_dim // 4),
            nn.LayerNorm(hidden_dim // 4),
            nn.ReLU(),
            nn.Dropout(dropout),
            
            nn.Linear(hidden_dim // 4, 1)
        )
        
        # 初始化权重
        self._init_weights()
        
    def _init_weights(self):
        """初始化网络权重"""
        for module in self.ahi_regressor:
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight)
                nn.init.constant_(module.bias, 0)
    
    def forward(self, x_dict: Dict[str, torch.Tensor]) -> torch.Tensor:
        """
        前向传播
        
        Args:
            x_dict: 包含5个信号的字典
        
        Returns:
            AHI预测值: (batch_size,)
        """
        # 希尔伯特降维
        hilbert_features = self.hilbert_mapper(x_dict)
        
        # AHI回归
        ahi_pred = self.ahi_regressor(hilbert_features).squeeze(-1)
        
        return ahi_pred
    
    @property
    def valid_signals(self):
        """返回模型支持的信号列表"""
        return ['ECG', 'SAO2', 'ABD', 'THX', 'AIRFLOW']
    
    def get_model_stats(self):
        """获取模型统计信息"""
        # 计算总参数量
        total_params = sum(p.numel() for p in self.parameters())
        trainable_params = sum(p.numel() for p in self.parameters() if p.requires_grad)
        
        # 获取压缩统计
        compression_stats = self.hilbert_mapper.get_compression_stats()
        
        # 获取性能统计
        performance_stats = self.hilbert_mapper.get_performance_stats()
        
        return {
            'total_parameters': total_params,
            'trainable_parameters': trainable_params,
            'compression_stats': compression_stats,
            'performance_stats': performance_stats,
            'model_size_mb': total_params * 4 / (1024 * 1024)  # 假设float32
        }
    
    def optimize_for_inference(self):
        """优化模型用于推理"""
        self.hilbert_mapper.optimize_for_inference()
        self.eval()
        
        print("HilbertAHIPredictor已优化用于推理")
        stats = self.get_model_stats()
        print(f"模型参数量: {stats['total_parameters']:,}")
        print(f"模型大小: {stats['model_size_mb']:.1f} MB")
