import pandas as pd
import os
from concurrent.futures import ThreadPoolExecutor

# 1. 读取 info.csv 中的数据并构造分类映射字典
info_df = pd.read_csv('classified_info.csv')
classification_map = dict(zip(info_df['nsrrid'], info_df['ahi_a0h3']))

def update_parquet_file(nsrrid, classification_value):
    parquet_file = f'/mnt/sda/xly/shhs/val/shhs1-{nsrrid}.parquet'
    if os.path.exists(parquet_file):
        try:
            # 读取 Parquet 文件
            parquet_df = pd.read_parquet(parquet_file)
            
            # 添加 classification 列
            parquet_df['ahi'] = classification_value
            
            # 保存修改后的 Parquet 文件
            parquet_df.to_parquet(parquet_file, index=False)
            print(f"Updated {parquet_file} with classification value: {classification_value}")
        except Exception as e:
            print(f"Error processing {parquet_file}: {e}")
    else:
        print(f"File not found: {parquet_file}")


# 3. 并行处理所有文件
with ThreadPoolExecutor() as executor:
    for nsrrid, classification_value in classification_map.items():
        executor.submit(update_parquet_file, nsrrid, classification_value)

print("All files processed.")

