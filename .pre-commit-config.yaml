repos:
# Common, useful hooks. See here for more: https://pre-commit.com/hooks.html
- repo: https://github.com/pre-commit/pre-commit-hooks
  rev: v4.4.0
  hooks:
  - id: check-added-large-files # Checks you haven't staged large files for Git
  - id: check-yaml # Checks YAML files are valid.
  - id: check-toml # Checks TOML files are valid.
  - id: debug-statements # Checks there aren't Python debugger statements lying around.
  - id: end-of-file-fixer # Make sure files are empty, or end with a newline.
  - id: trailing-whitespace # Removes trailing whitespace from files.
# Run all pre-commit hooks from local repo for improved speed & lower maintenance overheads
- repo: local
  hooks:
# Format code e.g. overlength lines.
  - id: ruff_format
    name: Formatting Python files using Ruff
    require_serial: true
    language: python
    types_or: [python]
    entry: ruff format
# Lint code i.e. check for PEP/style compliance.
  - id: ruff_lint
    name: Linting Python files using Ruff
    require_serial: true
    language: python
    types_or: [python]
    entry: ruff check
    args: [--fix] # https://docs.astral.sh/ruff/rules
