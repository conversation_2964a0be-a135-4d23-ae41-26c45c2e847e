"""TimesNet训练脚本，用于AHI回归任务"""

import os
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import numpy as np
import pandas as pd
from tqdm import tqdm
import logging
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
from scipy.stats import pearsonr
import matplotlib.pyplot as plt
import seaborn as sns

from wav2sleep.models.timesnet_adapter import TimesNetForAHI, SimpleTimesNetForAHI
from wav2sleep.models.hilbert_ahi_adapter import HilbertAHIPredictor
from wav2sleep.data.timesnet_dataset import create_timesnet_dataloader, denormalize_ahi_piecewise

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class R2Tracker:
    """实时R²追踪器"""
    def __init__(self, window_size=100):
        self.window_size = window_size
        self.reset()

    def reset(self):
        self.preds = []
        self.targets = []

    def update(self, pred_batch, target_batch):
        """更新预测值和真实值"""
        self.preds.extend(pred_batch)
        self.targets.extend(target_batch)

        # 保持窗口大小
        if len(self.preds) > self.window_size:
            self.preds = self.preds[-self.window_size:]
            self.targets = self.targets[-self.window_size:]

    def get_r2(self):
        """计算当前R²"""
        if len(self.preds) < 2:
            return 0.0
        return r2_score(self.targets, self.preds)

    def get_pearson(self):
        """计算当前Pearson相关系数"""
        if len(self.preds) < 2:
            return 0.0
        corr, _ = pearsonr(self.preds, self.targets)
        return corr

    def get_stats(self):
        """获取当前统计信息"""
        return {
            'r2': self.get_r2(),
            'pearson': self.get_pearson(),
            'samples': len(self.preds)
        }


class AHIRegressionTrainer:
    """AHI回归训练器"""

    def __init__(
        self,
        model,
        train_loader,
        val_loader,
        device='cuda',
        learning_rate=1e-4,
        weight_decay=1e-4,
        loss_type='huber'
    ):
        self.model = model.to(device)
        self.train_loader = train_loader
        self.val_loader = val_loader
        self.device = device

        # 优化器
        self.optimizer = optim.AdamW(
            model.parameters(),
            lr=learning_rate,
            weight_decay=weight_decay
        )

        # 损失函数
        if loss_type == 'mse':
            self.criterion = nn.MSELoss()
        elif loss_type == 'mae':
            self.criterion = nn.L1Loss()
        elif loss_type == 'huber':
            self.criterion = nn.HuberLoss(delta=1.0)
        else:
            raise ValueError(f"Unknown loss type: {loss_type}")

        # 学习率调度器
        self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer, mode='min', factor=0.5, patience=5, verbose=True
        )

        # 训练历史
        self.train_losses = []
        self.val_losses = []
        self.val_metrics = []

        # R²追踪器
        self.train_r2_tracker = R2Tracker(window_size=500)  # 追踪最近500个样本
        self.val_r2_tracker = R2Tracker(window_size=200)    # 验证集较小，追踪200个样本

    def train_epoch(self):
        """训练一个epoch"""
        self.model.train()
        total_loss = 0
        num_batches = 0

        # 用于计算R²的累积变量
        all_preds = []
        all_targets = []

        pbar = tqdm(self.train_loader, desc="Training")
        for batch_signals, batch_ahi in pbar:
            # 移动到设备
            batch_signals = {k: v.to(self.device) for k, v in batch_signals.items()}
            batch_ahi = batch_ahi.to(self.device)

            # 前向传播
            self.optimizer.zero_grad()
            pred_ahi = self.model(batch_signals)

            # 计算损失
            loss = self.criterion(pred_ahi, batch_ahi)

            # 反向传播
            loss.backward()

            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)

            self.optimizer.step()

            # 统计
            total_loss += loss.item()
            num_batches += 1

            # 收集预测值和真实值用于R²计算
            batch_preds = pred_ahi.detach().cpu().numpy()
            batch_targets = batch_ahi.detach().cpu().numpy()

            all_preds.extend(batch_preds)
            all_targets.extend(batch_targets)

            # 更新R²追踪器
            self.train_r2_tracker.update(batch_preds, batch_targets)
            train_stats = self.train_r2_tracker.get_stats()

            # 更新进度条，显示损失、R²和Pearson相关系数
            pbar.set_postfix({
                'loss': f'{loss.item():.4f}',
                'R²': f'{train_stats["r2"]:.4f}',
                'Pearson': f'{train_stats["pearson"]:.4f}',
                'samples': train_stats["samples"]
            })

        avg_loss = total_loss / num_batches
        final_r2 = r2_score(all_targets, all_preds) if len(all_preds) > 1 else 0.0
        final_pearson, _ = pearsonr(all_preds, all_targets) if len(all_preds) > 1 else (0.0, 1.0)

        self.train_losses.append(avg_loss)

        # 打印epoch总结
        logger.info(f"训练完成 - 损失: {avg_loss:.4f}, R²: {final_r2:.4f}, Pearson: {final_pearson:.4f}")

        return avg_loss

    def validate(self):
        """验证"""
        self.model.eval()
        total_loss = 0
        num_batches = 0

        all_preds = []
        all_targets = []

        with torch.no_grad():
            pbar = tqdm(self.val_loader, desc="Validation")
            for batch_signals, batch_ahi in pbar:
                # 移动到设备
                batch_signals = {k: v.to(self.device) for k, v in batch_signals.items()}
                batch_ahi = batch_ahi.to(self.device)

                # 前向传播
                pred_ahi = self.model(batch_signals)

                # 计算损失
                loss = self.criterion(pred_ahi, batch_ahi)

                total_loss += loss.item()
                num_batches += 1

                # 收集预测和真实值
                batch_preds = pred_ahi.cpu().numpy()
                batch_targets = batch_ahi.cpu().numpy()

                all_preds.extend(batch_preds)
                all_targets.extend(batch_targets)

                # 更新验证R²追踪器
                self.val_r2_tracker.update(batch_preds, batch_targets)
                val_stats = self.val_r2_tracker.get_stats()

                # 更新进度条
                pbar.set_postfix({
                    'val_loss': f'{loss.item():.4f}',
                    'val_R²': f'{val_stats["r2"]:.4f}',
                    'val_Pearson': f'{val_stats["pearson"]:.4f}',
                    'samples': val_stats["samples"]
                })

        avg_loss = total_loss / num_batches
        self.val_losses.append(avg_loss)

        # 计算评价指标
        metrics = self.calculate_metrics(all_preds, all_targets)
        self.val_metrics.append(metrics)

        return avg_loss, metrics

    def calculate_metrics(self, preds, targets):
        """计算评价指标"""
        preds = np.array(preds)
        targets = np.array(targets)

        # 反归一化（如果需要）
        # preds_denorm = [denormalize_ahi_piecewise(p) for p in preds]
        # targets_denorm = [denormalize_ahi_piecewise(t) for t in targets]

        # 计算指标
        mae = mean_absolute_error(targets, preds)
        mse = mean_squared_error(targets, preds)
        rmse = np.sqrt(mse)
        r2 = r2_score(targets, preds)

        # 计算MAPE（避免除零）
        mask = targets != 0
        if mask.sum() > 0:
            mape = np.mean(np.abs((targets[mask] - preds[mask]) / targets[mask])) * 100
        else:
            mape = float('inf')

        # 计算相关系数
        if len(preds) > 1:
            pearson_r, _ = pearsonr(preds, targets)
        else:
            pearson_r = 0.0

        return {
            'mae': mae,
            'rmse': rmse,
            'mape': mape,
            'r2': r2,
            'pearson': pearson_r
        }

    def train(self, num_epochs=50, save_dir='./checkpoints'):
        """训练模型"""
        os.makedirs(save_dir, exist_ok=True)

        best_val_loss = float('inf')
        patience_counter = 0
        max_patience = 10

        logger.info(f"开始训练，共 {num_epochs} 个epoch")

        for epoch in range(num_epochs):
            logger.info(f"\nEpoch {epoch+1}/{num_epochs}")

            # 重置R²追踪器
            self.train_r2_tracker.reset()
            self.val_r2_tracker.reset()

            # 训练
            train_loss = self.train_epoch()

            # 验证
            val_loss, val_metrics = self.validate()

            # 学习率调度
            self.scheduler.step(val_loss)

            # 打印详细结果
            logger.info(f"Epoch {epoch+1}/{num_epochs} 完成:")
            logger.info(f"  训练损失: {train_loss:.4f}")
            logger.info(f"  验证损失: {val_loss:.4f}")
            logger.info(f"  验证指标:")
            logger.info(f"    MAE: {val_metrics['mae']:.4f}")
            logger.info(f"    RMSE: {val_metrics['rmse']:.4f}")
            logger.info(f"    R²: {val_metrics['r2']:.4f}")
            logger.info(f"    Pearson: {val_metrics['pearson']:.4f}")
            logger.info(f"    MAPE: {val_metrics['mape']:.2f}%")
            logger.info(f"  当前学习率: {self.optimizer.param_groups[0]['lr']:.6f}")

            # 保存最佳模型
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                patience_counter = 0

                torch.save({
                    'epoch': epoch,
                    'model_state_dict': self.model.state_dict(),
                    'optimizer_state_dict': self.optimizer.state_dict(),
                    'val_loss': val_loss,
                    'val_metrics': val_metrics
                }, os.path.join(save_dir, 'best_model.pth'))

                logger.info(f"🎉 保存最佳模型!")
                logger.info(f"  验证损失: {val_loss:.4f} (改善: {best_val_loss - val_loss:.4f})")
                logger.info(f"  验证R²: {val_metrics['r2']:.4f}")
            else:
                patience_counter += 1
                logger.info(f"⏳ 验证损失无改善 ({patience_counter}/{max_patience})")

            # 早停
            if patience_counter >= max_patience:
                logger.info(f"早停，{max_patience} 个epoch无改善")
                break

        logger.info("训练完成")
        return self.train_losses, self.val_losses, self.val_metrics


def main():
    """主函数"""
    # 配置
    config = {
        'data_folder': 'E:/OSA/shhs_processed',
        'label_csv_path': './shhs1_label.csv',
        'batch_size': 1,  # 较小的batch size，因为序列很长
        'learning_rate': 1e-4,
        'weight_decay': 1e-4,
        'num_epochs': 100,
        'device': 'cuda' if torch.cuda.is_available() else 'cpu',
        'model_type': 'hilbert'  # 'simple', 'full', or 'hilbert'
    }
    
    logger.info(f"使用设备: {config['device']}")

    # 创建数据加载器
    logger.info("创建数据加载器...")
    train_loader = create_timesnet_dataloader(
        data_folder=config['data_folder'],
        label_csv_path=config['label_csv_path'],
        batch_size=config['batch_size'],
        split='train',
        normalize_ahi=True,
        num_workers=0  # Windows上设为0
    )

    val_loader = create_timesnet_dataloader(
        data_folder=config['data_folder'],
        label_csv_path=config['label_csv_path'],
        batch_size=config['batch_size'],
        split='val',
        normalize_ahi=True,
        num_workers=0
    )

    logger.info(f"训练集: {len(train_loader.dataset)} 样本")
    logger.info(f"验证集: {len(val_loader.dataset)} 样本")

    # 创建模型
    logger.info("创建模型...")
    if config['model_type'] == 'simple':
        model = SimpleTimesNetForAHI(d_model=256, num_layers=3)
    elif config['model_type'] == 'hilbert':
        model = HilbertAHIPredictor(
            ecg_output_dim=128,
            other_output_dim=64,
            hidden_dim=256,
            dropout=0.3,
            gamma=0.01
        )
    else:
        model = TimesNetForAHI()

    logger.info(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")

    # 如果是希尔伯特模型，打印详细统计
    if config['model_type'] == 'hilbert':
        stats = model.get_model_stats()
        logger.info(f"压缩比: {stats['compression_stats']['compression_ratio']:.1f}x")
        logger.info(f"内存减少: {stats['compression_stats']['memory_reduction']}")
        logger.info(f"模型大小: {stats['model_size_mb']:.1f} MB")

    # 创建训练器
    trainer = AHIRegressionTrainer(
        model=model,
        train_loader=train_loader,
        val_loader=val_loader,
        device=config['device'],
        learning_rate=config['learning_rate'],
        weight_decay=config['weight_decay'],
        loss_type='huber'
    )

    # 训练
    train_losses, val_losses, val_metrics = trainer.train(
        num_epochs=config['num_epochs']
    )

    logger.info("训练完成！")


if __name__ == "__main__":
    main()
